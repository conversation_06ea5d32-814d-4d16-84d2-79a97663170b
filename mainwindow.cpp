#include "mainwindow.h"
#include "./ui_mainwindow.h"
#include "previewwindow.h"
#include <QGridLayout>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QPushButton>
#include <QStackedWidget>
#include <QDialog>
#include <QLineEdit>
#include <QFormLayout>
#include <QDialogButtonBox>
#include <QMessageBox>
#include <QJsonDocument>
#include <QJsonArray>
#include <QJsonObject>
#include <QStandardPaths>
#include <QDir>
#include <QFile>
#include <QScrollArea>
#include <QCoreApplication>
#include <QDateTime>
#include <QResizeEvent>
#include <QShowEvent>
#include <QThread>
#include <QTimer>
#include "XCloudSDK/XCloudSDK.h"
#include "XNetSDK/XNetSDKDefine.h"

// 定义设备IP属性常量（如果SDK中没有定义）
#ifndef ATTR_GET_DEV_IP_BY_HANDLE
#define ATTR_GET_DEV_IP_BY_HANDLE 11100
#endif

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
    , sdkInitialized(false)
    , hUser(-1)
    , alarmMonitoringActive(false)
    , qrCodeDetector(nullptr)
    , deviceThreadPool(nullptr)
    , deviceStatusTimer(nullptr)
    , loginSemaphore(nullptr)
    , previewSemaphore(nullptr)
{
    ui->setupUi(this);

    // 重新启用XCloudSDK初始化，参考demo的安全方式
    qDebug() << "Attempting to initialize XCloudSDK...";
    if (initializeSDK()) {
        sdkInitialized = true;
        qDebug() << "XCloudSDK initialized successfully";
    } else {
        qDebug() << "XCloudSDK initialization failed";
        // 暂时不显示消息框，避免可能的问题
        sdkInitialized = false;
    }
    
    // 初始化资源管理
    initializeResourceManagement();

    // 加载设备列表
    loadDeviceList();

    // 初始化二维码检测器
    qrCodeDetector = new QRCodeDetector();
    QString modelPath = QCoreApplication::applicationDirPath() + "/models";
    qDebug() << "Application directory:" << QCoreApplication::applicationDirPath();
    qDebug() << "Model path:" << modelPath;
    qDebug() << "Model directory exists:" << QDir(modelPath).exists();

    if (qrCodeDetector->initialize(modelPath)) {
        qDebug() << "QR code detector initialized successfully";
    } else {
        qWarning() << "QR code detector initialization failed:" << qrCodeDetector->getLastError();
        qWarning() << "Please check if model files exist in:" << modelPath;
    }

    // 创建堆叠窗口部件
    stackedWidget = new QStackedWidget;

    // 设置堆叠窗口部件为中央窗口部件
    setCentralWidget(stackedWidget);

    // 设置窗口标题
    setWindowTitle("摄像头管理");

    // 创建设备列表页面
    setupDeviceListPage();

    // 默认显示设备列表页面
    stackedWidget->setCurrentWidget(deviceListPage);

    // 测试二维码检测器
    QTimer::singleShot(2000, this, &MainWindow::testQRCodeDetector);
}

void MainWindow::setupDeviceListPage()
{
    // 创建设备列表页面
    deviceListPage = new QWidget;

    // 创建主垂直布局
    QVBoxLayout *mainLayout = new QVBoxLayout;

    // 创建顶部水平布局（包含标题、添加设备按钮和返回按钮）
    QHBoxLayout *topLayout = new QHBoxLayout;

    // 创建标题Label
    QLabel *titleLabel = new QLabel("设备列表");
    titleLabel->setStyleSheet("font-size: 20px; font-weight: bold;");

    // 创建添加设备按钮
    QPushButton *addDeviceBtn = new QPushButton("+ 添加设备");
    addDeviceBtn->setMaximumWidth(100);
    addDeviceBtn->setStyleSheet("font-size: 14px; padding: 5px; background-color: #4CAF50; color: white; border: none; border-radius: 3px;");

    // 将标题和按钮添加到顶部布局
    topLayout->addWidget(titleLabel);
    topLayout->addStretch();
    topLayout->addWidget(addDeviceBtn);

    // 创建滚动区域
    QScrollArea *scrollArea = new QScrollArea;
    scrollArea->setWidgetResizable(true);
    scrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    scrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);

    // 创建滚动区域内的内容widget
    QWidget *scrollContent = new QWidget;
    
    // 创建网格布局
    QGridLayout *gridLayout = new QGridLayout;
    gridLayout->setSpacing(15);

    // 固定每行3列
    const int columns = 3;
    
    if (deviceList.isEmpty()) {
        // 如果没有设备，显示空的3x3网格
        const int rows = 3;
        const int totalSlots = rows * columns;
        
        for (int i = 0; i < totalSlots; i++) {
            int row = i / columns;
            int col = i % columns;
            
            QVBoxLayout *itemLayout = new QVBoxLayout;
            
            QLabel *emptyLabel = new QLabel("空位");
            emptyLabel->setAlignment(Qt::AlignCenter);
            emptyLabel->setStyleSheet("font-size: 14px; color: #ccc; border: 2px dashed #ddd; padding: 20px; border-radius: 5px; background-color: #fafafa;");
            emptyLabel->setMinimumHeight(160);
            emptyLabel->setMaximumHeight(160);
            
            itemLayout->addWidget(emptyLabel);
            
            gridLayout->addLayout(itemLayout, row, col);
        }
    } else {
        // 计算需要的行数（至少3行）
        int neededRows = qMax(3, (deviceList.size() + columns - 1) / columns);
        int totalSlots = neededRows * columns;

        for (int i = 0; i < totalSlots; i++) {
            int row = i / columns;
            int col = i % columns;
            
            // 为每个位置创建垂直布局
            QVBoxLayout *itemLayout = new QVBoxLayout;

            if (i < deviceList.size()) {
                // 有设备数据，显示设备信息
                const QMap<QString, QString>& device = deviceList[i];
                
                // 创建外围容器，添加边框样式（类似空位的样式）
                QWidget *outerContainer = new QWidget;
                outerContainer->setStyleSheet("border: 2px dashed #ddd; border-radius: 5px; background-color: #fafafa; padding: 5px;");
                outerContainer->setMinimumHeight(160);
                outerContainer->setMaximumHeight(160);

                // 创建外围容器的布局
                QVBoxLayout *outerLayout = new QVBoxLayout(outerContainer);
                outerLayout->setContentsMargins(5, 5, 5, 5);
                outerLayout->setSpacing(0);

                // 创建设备容器，使用自定义容器类
                DeviceContainerWidget *deviceContainer = new DeviceContainerWidget;
                deviceContainer->setStyleSheet("border: 2px solid #4CAF50; border-radius: 5px; background-color: #f9f9f9;");
                deviceContainer->setMinimumHeight(150);
                deviceContainer->setMaximumHeight(150);

                QVBoxLayout *deviceLayout = new QVBoxLayout(deviceContainer);
                deviceLayout->setContentsMargins(10, 10, 10, 10);
                deviceLayout->setSpacing(5);

                // 将设备容器添加到外围容器
                outerLayout->addWidget(deviceContainer);

                // 创建删除按钮（叉子形式，放在右上角）
                QPushButton *deleteBtn = new QPushButton("×");
                deleteBtn->setParent(deviceContainer);
                deleteBtn->setFixedSize(20, 20);
                deleteBtn->setProperty("deviceId", device["deviceId"]);
                deleteBtn->setStyleSheet(
                    "QPushButton {"
                    "background-color: #f44336; "
                    "color: white; "
                    "font-weight: bold; "
                    "font-size: 14px; "
                    "border: none; "
                    "border-radius: 10px; "
                    "}"
                    "QPushButton:hover {"
                    "background-color: #d32f2f; "
                    "}"
                );

                // 设置删除按钮到容器
                deviceContainer->setDeleteButton(deleteBtn);

                // 连接删除按钮点击事件
                connect(deleteBtn, &QPushButton::clicked, this, [this, deviceId = device["deviceId"]]() {
                    onDeleteDevice(deviceId);
                });

                // 创建设备标签，显示设备名称
                QLabel *label = new QLabel(device["deviceName"]);
                label->setAlignment(Qt::AlignCenter);
                label->setStyleSheet("font-size: 12px; font-weight: bold; color: #333; border: none;");
                label->setWordWrap(true);
                label->setMaximumHeight(25);

                // 创建实时预览按钮
                QPushButton *previewBtn = new QPushButton("实时预览");
                previewBtn->setMinimumHeight(25);
                previewBtn->setMaximumHeight(25);
                previewBtn->setProperty("deviceId", device["deviceId"]);
                previewBtn->setStyleSheet(
                    "QPushButton {"
                    "font-size: 10px; "
                    "border: 1px solid #ccc; "
                    "border-radius: 3px; "
                    "background-color: white; "
                    "}"
                    "QPushButton:hover {"
                    "background-color: #f0f0f0; "
                    "border: 1px solid #999; "
                    "}"
                );

                // 创建修改按钮
                QPushButton *editBtn = new QPushButton("修改");
                editBtn->setMinimumHeight(25);
                editBtn->setMaximumHeight(25);
                editBtn->setProperty("deviceId", device["deviceId"]);
                editBtn->setStyleSheet(
                    "QPushButton {"
                    "font-size: 10px; "
                    "border: 1px solid #ccc; "
                    "border-radius: 3px; "
                    "background-color: #FFC107; "
                    "color: white; "
                    "}"
                    "QPushButton:hover {"
                    "background-color: #FFB300; "
                    "border: 1px solid #FF8F00; "
                    "}"
                );

                // 连接实时预览按钮点击事件 - 使用槽函数而不是lambda
                previewBtn->setProperty("deviceId", device["deviceId"]);
                connect(previewBtn, &QPushButton::clicked, this, &MainWindow::onPreviewButtonClicked);

                // 连接修改按钮点击事件
                editBtn->setProperty("deviceId", device["deviceId"]);
                connect(editBtn, &QPushButton::clicked, this, &MainWindow::onEditButtonClicked);

                // 将控件添加到设备布局（不再包含删除按钮）
                deviceLayout->addWidget(label);
                deviceLayout->addWidget(previewBtn);
                deviceLayout->addWidget(editBtn);
                // deviceLayout->addWidget(photoBtn);  // 拍照按钮已注释，暂时不添加
                deviceLayout->addStretch();
                
                // 将外围容器添加到项目布局
                itemLayout->addWidget(outerContainer);
            } else {
                // 没有设备数据，显示空白占位符
                QLabel *emptyLabel = new QLabel("空位");
                emptyLabel->setAlignment(Qt::AlignCenter);
                emptyLabel->setStyleSheet("font-size: 14px; color: #ccc; border: 2px dashed #ddd; padding: 20px; border-radius: 5px; background-color: #fafafa;");
                emptyLabel->setMinimumHeight(160);
                emptyLabel->setMaximumHeight(160);
                
                itemLayout->addWidget(emptyLabel);
            }
            
            // 将垂直布局添加到网格布局
            gridLayout->addLayout(itemLayout, row, col);
        }
    }

    // 设置滚动内容的布局
    scrollContent->setLayout(gridLayout);
    scrollArea->setWidget(scrollContent);

    // 将顶部布局和滚动区域添加到主布局
    mainLayout->addLayout(topLayout);
    mainLayout->addSpacing(10);
    mainLayout->addWidget(scrollArea);
    
    // 如果没有设备，在底部显示提示信息
    if (deviceList.isEmpty()) {
        QLabel *emptyLabel = new QLabel("暂无设备，请先添加设备");
        emptyLabel->setAlignment(Qt::AlignCenter);
        emptyLabel->setStyleSheet("font-size: 16px; color: #888; margin: 20px;");
        mainLayout->addWidget(emptyLabel);
    }

    // 设置设备列表页面布局
    deviceListPage->setLayout(mainLayout);

    // 将设备列表页面添加到堆叠窗口部件
    stackedWidget->addWidget(deviceListPage);

    // 连接添加设备按钮点击事件
    connect(addDeviceBtn, &QPushButton::clicked, this, &MainWindow::showAddDeviceDialog);
}

void MainWindow::showDeviceList()
{
    // 每次显示前重新创建设备列表页面，以确保显示最新数据
    if (deviceListPage) {
        stackedWidget->removeWidget(deviceListPage);
        delete deviceListPage;
        deviceListPage = nullptr;
    }
    
    setupDeviceListPage();
    stackedWidget->setCurrentWidget(deviceListPage);
}

void MainWindow::showHomePage()
{
    stackedWidget->setCurrentWidget(homePage);
}

MainWindow::~MainWindow()
{
    qDebug() << "MainWindow destructor called";

    try {
        // 清理资源管理
        cleanupResourceManagement();

        if (sdkInitialized) {
            qDebug() << "Cleaning up SDK resources...";

            // 停止报警监听
            stopAlarmMonitoring();

            // 登出所有设备（线程安全）
            QStringList deviceIds;
            {
                QMutexLocker locker(&deviceMapMutex);
                deviceIds = deviceLoginHandles.keys();
            }

            qDebug() << "Logging out" << deviceIds.size() << "devices...";
            for (const QString& deviceId : deviceIds) {
                qDebug() << "Logging out device:" << deviceId;
                logoutDevice(deviceId);
            }

            // 清空设备相关的映射（线程安全）
            {
                QMutexLocker locker(&deviceMapMutex);
                deviceLoginHandles.clear();
                deviceLoginStatus.clear();
                devicePlayHandles.clear();
            }

            // 等待一小段时间确保登出完成
            QThread::msleep(100);

            // 注销回调函数
            if (hUser >= 0) {
                qDebug() << "Unregistering SDK callback...";
                XCloudSDK_UnRegister(hUser);
                hUser = -1;
            }

            // SDK反初始化
            qDebug() << "Uninitializing SDK...";
            uninitializeSDK();
        }

        // 清理二维码检测器
        if (qrCodeDetector) {
            qDebug() << "Deleting QR code detector...";
            delete qrCodeDetector;
            qrCodeDetector = nullptr;
        }

        qDebug() << "Deleting UI...";
        delete ui;
        ui = nullptr;

        qDebug() << "MainWindow destructor completed successfully";

    } catch (const std::exception& e) {
        qCritical() << "Exception in MainWindow destructor:" << e.what();
    } catch (...) {
        qCritical() << "Unknown exception in MainWindow destructor";
    }
}

void MainWindow::closeEvent(QCloseEvent *event)
{
    qDebug() << "MainWindow closeEvent called";

    try {
        // 确保在窗口关闭前清理所有资源
        if (sdkInitialized) {
            qDebug() << "Cleaning up resources before closing...";

            // 停止报警监听
            stopAlarmMonitoring();

            // 登出所有设备
            QStringList deviceIds = deviceLoginHandles.keys();
            for (const QString& deviceId : deviceIds) {
                qDebug() << "Logging out device before close:" << deviceId;
                logoutDevice(deviceId);
            }

            // 清空映射
            deviceLoginHandles.clear();
            deviceLoginStatus.clear();
            devicePlayHandles.clear();

            // 等待一小段时间确保操作完成
            QThread::msleep(50);
        }

        qDebug() << "Accepting close event";
        event->accept();

    } catch (const std::exception& e) {
        qCritical() << "Exception in closeEvent:" << e.what();
        event->accept(); // 即使出现异常也要关闭窗口
    } catch (...) {
        qCritical() << "Unknown exception in closeEvent";
        event->accept(); // 即使出现异常也要关闭窗口
    }
}

void MainWindow::showAddDeviceDialog()
{
    AddDeviceDialog dialog(this);
    if (dialog.exec() == QDialog::Accepted) {
        QString deviceName = dialog.getDeviceName();
        QString deviceId = dialog.getDeviceId();
        QString username = dialog.getUsername();
        QString password = dialog.getPassword();
        
        // 使用addDevice方法，它会自动保存
        if (addDevice(deviceName, deviceId, username, password)) {
            QMessageBox::information(this, "成功", QString("设备已添加: %1").arg(deviceId));
            // 重新显示设备列表页面以更新界面
            showDeviceList();
        } else {
            QMessageBox::warning(this, "警告", "该设备已存在！");
        }
    }
}

bool MainWindow::addDevice(const QString& deviceName, const QString& deviceId, const QString& username, const QString& password)
{
    // 检查设备是否已存在
    for (const auto& device : deviceList) {
        if (device["deviceId"] == deviceId) {
            return false; // 设备已存在
        }
    }
    
    QMap<QString, QString> deviceInfo;
    deviceInfo["deviceName"] = deviceName;
    deviceInfo["deviceId"] = deviceId;
    deviceInfo["username"] = username;
    deviceInfo["password"] = password;
    deviceInfo["addTime"] = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
    
    deviceList.append(deviceInfo);
    saveDeviceList();  // 立即保存
    return true;
}

bool MainWindow::removeDevice(const QString& deviceId)
{
    for (int i = 0; i < deviceList.size(); ++i) {
        if (deviceList[i]["deviceId"] == deviceId) {
            deviceList.removeAt(i);
            saveDeviceList();  // 立即保存
            return true;
        }
    }
    return false;
}

QMap<QString, QString> MainWindow::getDevice(const QString& deviceId)
{
    for (const auto& device : deviceList) {
        if (device["deviceId"] == deviceId) {
            return device;
        }
    }
    return QMap<QString, QString>(); // 返回空映射表示未找到
}

void MainWindow::saveDeviceList()
{
    QString dataDir = QCoreApplication::applicationDirPath() + "/data";
    QString filePath = dataDir + "/devices.json";
    
    // 确保data目录存在
    QDir dir;
    if (!dir.exists(dataDir)) {
        dir.mkpath(dataDir);
    }
    
    // 转换为JSON格式
    QJsonArray jsonArray;
    for (const auto& device : deviceList) {
        QJsonObject deviceObj;
        for (auto it = device.begin(); it != device.end(); ++it) {
            deviceObj[it.key()] = it.value();
        }
        jsonArray.append(deviceObj);
    }
    
    QJsonDocument doc(jsonArray);
    
    // 写入文件
    QFile file(filePath);
    if (file.open(QIODevice::WriteOnly)) {
        file.write(doc.toJson());
        file.close();
        qDebug() << "Device list saved to:" << filePath;
    } else {
        qWarning() << "Unable to save device list to file:" << filePath;
    }
}

void MainWindow::loadDeviceList()
{
    QString dataDir = QCoreApplication::applicationDirPath() + "/data";
    QString filePath = dataDir + "/devices.json";
    
    QFile file(filePath);
    if (!file.exists()) {
        qDebug() << "Device list file does not exist, using empty list";
        return;
    }
    
    if (file.open(QIODevice::ReadOnly)) {
        QByteArray data = file.readAll();
        file.close();
        
        QJsonDocument doc = QJsonDocument::fromJson(data);
        if (doc.isArray()) {
            deviceList.clear();
            QJsonArray jsonArray = doc.array();
            
            for (const auto& value : jsonArray) {
                if (value.isObject()) {
                    QJsonObject deviceObj = value.toObject();
                    QMap<QString, QString> device;
                    
                    for (auto it = deviceObj.begin(); it != deviceObj.end(); ++it) {
                        device[it.key()] = it.value().toString();
                    }
                    deviceList.append(device);
                }
            }
            qDebug() << "Loaded" << deviceList.size() << "devices";

            // 重新启用设备登录功能，添加更多安全检查
            if (sdkInitialized && hUser >= 0 && deviceList.size() > 0) {
                qDebug() << "Scheduling device login for" << deviceList.size() << "devices...";

                // 使用QTimer延迟执行设备登录，确保主窗口完全初始化后再执行
                QTimer::singleShot(2000, this, [this]() {
                    // 再次检查对象是否仍然有效
                    if (!this || !sdkInitialized || hUser < 0) {
                        qWarning() << "MainWindow or SDK invalid during delayed login";
                        return;
                    }

                    startAlarmMonitoring();
                    qDebug() << "Starting delayed device login...";

                    qDebug() << "Starting login for all" << deviceList.size() << "devices";

                    // 使用并发登录替代串行登录
                    loginDevicesConcurrently();
                });
            } else {
                qWarning() << "SDK not properly initialized or no devices, skipping device login. SDK initialized:" << sdkInitialized << "hUser:" << hUser << "Device count:" << deviceList.size();
            }
        }
    } else {
        qWarning() << "Unable to read device list file:" << filePath;
    }
}

void MainWindow::onDeleteDevice(const QString& deviceId)
{
    // 弹出确认对话框
    QMessageBox::StandardButton reply = QMessageBox::question(
        this, 
        "确认删除", 
        QString("确定要删除设备 %1 吗？").arg(deviceId),
        QMessageBox::Yes | QMessageBox::No
    );
    
    if (reply == QMessageBox::Yes) {
        if (removeDevice(deviceId)) {
            QMessageBox::information(this, "成功", "设备已删除");
            // 重新显示设备列表页面以更新界面
            showDeviceList();
        } else {
            QMessageBox::warning(this, "错误", "删除设备失败");
        }
    }
}

// 添加设备对话框实现
AddDeviceDialog::AddDeviceDialog(QWidget *parent) : QDialog(parent)
{
    setWindowTitle("添加设备");
    setFixedSize(450, 460);  // 增加高度
    setModal(true);
    
    // 创建主布局
    QVBoxLayout *mainLayout = new QVBoxLayout;
    mainLayout->setContentsMargins(25, 25, 25, 25);
    mainLayout->setSpacing(15);
    
    // 创建标题
    QLabel *titleLabel = new QLabel("添加新设备");
    titleLabel->setAlignment(Qt::AlignCenter);
    titleLabel->setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50; margin-bottom: 10px;");
    
    // 创建表单布局
    QVBoxLayout *formLayout = new QVBoxLayout;
    formLayout->setSpacing(12);

    // 设备名称
    QLabel *deviceNameLabel = new QLabel("设备名称");
    deviceNameLabel->setStyleSheet("font-size: 14px; font-weight: bold; color: #333333;");
    deviceNameEdit = new QLineEdit;
    deviceNameEdit->setMinimumHeight(35);
    deviceNameEdit->setStyleSheet("padding: 8px; border: 2px solid #ddd; border-radius: 5px; font-size: 14px; background-color: white;");
    
    // 设备序列号/IP输入
    QLabel *deviceIdLabel = new QLabel("设备IP");
    deviceIdLabel->setStyleSheet("font-size: 14px; font-weight: bold; color: #333333;");
    deviceIdEdit = new QLineEdit;
    deviceIdEdit->setText("************:34567");
    deviceIdEdit->setPlaceholderText("例: ************:34567");
    deviceIdEdit->setMinimumHeight(35);
    deviceIdEdit->setStyleSheet("padding: 8px; border: 2px solid #ddd; border-radius: 5px; font-size: 14px; background-color: white;");
    
    // 用户名输入
    QLabel *usernameLabel = new QLabel("用户名");
    usernameLabel->setStyleSheet("font-size: 14px; font-weight: bold; color: #333333;");
    usernameEdit = new QLineEdit;
    usernameEdit->setText("admin");
    usernameEdit->setMinimumHeight(35);
    usernameEdit->setStyleSheet("padding: 8px; border: 2px solid #ddd; border-radius: 5px; font-size: 14px; background-color: white;");
    
    // 密码输入
    QLabel *passwordLabel = new QLabel("密码");
    passwordLabel->setStyleSheet("font-size: 14px; font-weight: bold; color: #333333;");
    passwordEdit = new QLineEdit;
    passwordEdit->setEchoMode(QLineEdit::Password);
    passwordEdit->setMinimumHeight(35);
    passwordEdit->setStyleSheet("padding: 8px; border: 2px solid #ddd; border-radius: 5px; font-size: 14px; background-color: white;");
    
    // 添加到表单布局
    formLayout->addWidget(deviceNameLabel);
    formLayout->addWidget(deviceNameEdit);
    formLayout->addSpacing(8);
    formLayout->addWidget(deviceIdLabel);
    formLayout->addWidget(deviceIdEdit);
    formLayout->addSpacing(8);
    formLayout->addWidget(usernameLabel);
    formLayout->addWidget(usernameEdit);
    formLayout->addSpacing(8);
    formLayout->addWidget(passwordLabel);
    formLayout->addWidget(passwordEdit);
    
    // 创建按钮布局
    QHBoxLayout *buttonLayout = new QHBoxLayout;
    buttonLayout->setSpacing(15);
    
    QPushButton *okButton = new QPushButton("确定");
    QPushButton *cancelButton = new QPushButton("取消");
    
    okButton->setMinimumHeight(35);
    okButton->setMinimumWidth(80);
    okButton->setStyleSheet("background-color: #4CAF50; color: white; border: none; padding: 8px 15px; font-size: 14px; font-weight: bold; border-radius: 5px;");
    
    cancelButton->setMinimumHeight(35);
    cancelButton->setMinimumWidth(80);
    cancelButton->setStyleSheet("background-color: #f44336; color: white; border: none; padding: 8px 15px; font-size: 14px; font-weight: bold; border-radius: 5px;");
    
    buttonLayout->addStretch();
    buttonLayout->addWidget(cancelButton);
    buttonLayout->addWidget(okButton);
    buttonLayout->addStretch();
    
    // 组装主布局
    mainLayout->addWidget(titleLabel);
    mainLayout->addSpacing(10);
    mainLayout->addLayout(formLayout);
    mainLayout->addStretch();
    mainLayout->addLayout(buttonLayout);
    
    setLayout(mainLayout);
    
    // 设置对话框背景
    setStyleSheet("QDialog { background-color: #f5f5f5; }");
    
    // 连接信号
    connect(okButton, &QPushButton::clicked, this, &AddDeviceDialog::onOkClicked);
    connect(cancelButton, &QPushButton::clicked, this, &QDialog::reject);
    
    // 设置焦点到第一个输入框
    deviceNameEdit->setFocus();
}

QString AddDeviceDialog::getDeviceName() const { return deviceNameEdit->text(); }
QString AddDeviceDialog::getDeviceId() const { return deviceIdEdit->text(); }
QString AddDeviceDialog::getUsername() const { return usernameEdit->text(); }
QString AddDeviceDialog::getPassword() const { return passwordEdit->text(); }

void AddDeviceDialog::onOkClicked()
{
    if (deviceNameEdit->text().isEmpty() ||deviceIdEdit->text().isEmpty() || usernameEdit->text().isEmpty()) {
        QMessageBox::warning(this, "警告", "请填写设备名称、序列号、用户名");
        return;
    }
    accept();
}

// DeviceContainerWidget 类实现
DeviceContainerWidget::DeviceContainerWidget(QWidget *parent)
    : QWidget(parent), deleteButton(nullptr)
{
}

void DeviceContainerWidget::setDeleteButton(QPushButton *button)
{
    deleteButton = button;
    updateDeleteButtonPosition();
}

void DeviceContainerWidget::updateDeleteButtonPosition()
{
    if (deleteButton) {
        deleteButton->move(width() - 25, 5);
        deleteButton->raise(); // 确保按钮在最上层
        deleteButton->show();
    }
}

void DeviceContainerWidget::resizeEvent(QResizeEvent *event)
{
    QWidget::resizeEvent(event);
    updateDeleteButtonPosition();
}

void DeviceContainerWidget::showEvent(QShowEvent *event)
{
    QWidget::showEvent(event);
    updateDeleteButtonPosition();
}

// XCloudSDK 相关方法实现
bool MainWindow::initializeSDK()
{
    qDebug() << "Starting SDK initialization...";

    // 参考demo，先设置日志级别
    XCloudSDK_SetLogTypeAndLevel(2, 1); // 参考demo的设置：输出到文件，debug级别

    // 使用更简单的初始化参数，参考demo
    const char* initParam = R"({
        "LogLevel": 1,
        "TempPath": "",
        "ConfigPath": "",
        "SaveFileType": "mp4"
    })";

    qDebug() << "Calling XCloudSDK_Init...";
    int result = XCloudSDK_Init(initParam);
    qDebug() << "XCloudSDK_Init result:" << result;

    if (result >= 0) {
        qDebug() << "SDK initialization successful, registering callback function...";
        // 注册回调函数，参考demo
        hUser = XCloudSDK_RegisterCallback((PXSDK_MessageCallBack)onSDKMessage, this);
        qDebug() << "Callback registration result:" << hUser;

        if (hUser >= 0) {
            qDebug() << "SDK callback function registered successfully, user handle:" << hUser;
            return true;
        } else {
            qWarning() << "SDK callback function registration failed, error code:" << hUser;
            XCloudSDK_UnInit();
            return false;
        }
    } else {
        qWarning() << "SDK initialization failed, error code:" << result;
        return false;
    }
}


void MainWindow::uninitializeSDK()
{
    qDebug() << "uninitializeSDK called";

    // 停止所有预览
    for (auto it = devicePlayHandles.begin(); it != devicePlayHandles.end(); ++it) {
        qDebug() << "Stopping media play for handle:" << it.value();
        XCloudSDK_Device_StopMediaPlay(it.value());
    }

    devicePlayHandles.clear();
    previewWidgets.clear();

    // 注销回调函数（只有在析构函数中没有注销的情况下才注销）
    // 析构函数已经处理了回调函数注销，这里不再重复处理

    qDebug() << "Calling XCloudSDK_UnInit()";
    XCloudSDK_UnInit();
    sdkInitialized = false;
    qDebug() << "SDK uninitialized";
}

bool MainWindow::connectToDevice(const QString& deviceId, const QString& username, const QString& password)
{
    qDebug() << "connectToDevice called for:" << deviceId << "username:" << username;

    if (!sdkInitialized) {
        qWarning() << "SDK not initialized";
        return false;
    }

    if (hUser < 0) {
        qWarning() << "Invalid user handle:" << hUser;
        return false;
    }

    qDebug() << "Calling XCloudSDK_Device_SetLocalUserNameAndPwd...";
    // 缓存设备用户名和密码到本地（参考demo代码）
    int result = XCloudSDK_Device_SetLocalUserNameAndPwd(
        deviceId.toUtf8().constData(),
        username.toUtf8().constData(),
        password.toUtf8().constData()
    );

    qDebug() << "XCloudSDK_Device_SetLocalUserNameAndPwd result:" << result;

    if (result == 0) {
        qDebug() << "Device credentials cached successfully for:" << deviceId;

        // 暂时不调用getDeviceConfig，先测试基本连接
        qDebug() << "Device connection completed successfully";
        return true;
    } else {
        qWarning() << "Failed to cache device credentials for:" << deviceId << "Error:" << result;
        return false;
    }
}

bool MainWindow::startPreview(const QString& deviceId, QWidget* previewWidget)
{
    if (!sdkInitialized) {
        qWarning() << "SDK not initialized";
        return false;
    }

    if (!previewWidget) {
        qWarning() << "Preview widget is null";
        return false;
    }

    qWarning() << "Start preview not implemented yet";
    return false;
}

bool MainWindow::stopPreview(const QString& deviceId)
{
    if (!devicePlayHandles.contains(deviceId)) {
        return false;
    }

    int playHandle = devicePlayHandles[deviceId];

    // 停止预览
    // int result = XCloudSDK_Play_StopRealPlay(playHandle);

    devicePlayHandles.remove(deviceId);
    previewWidgets.remove(deviceId);

    // return result >= 0;
    qWarning() << "Stop preview not implemented yet";
    return false;
}

bool MainWindow::captureImage(const QString& deviceId, const QString& filePath)
{
    if (!devicePlayHandles.contains(deviceId)) {
        qWarning() << "Device not in preview mode";
        return false;
    }

    int playHandle = devicePlayHandles[deviceId];

    // 抓图
    int result = XCloudSDK_Play_MediaSnapImage(playHandle, filePath.toUtf8().constData());

    return result >= 0;
}

bool MainWindow::getDeviceConfig(const QString& deviceId)
{
    if (!sdkInitialized || hUser < 0) {
        qWarning() << "SDK not initialized or callback not registered";
        return false;
    }

    // 获取设备系统配置信息
    int result = XCloudSDK_Device_DevGetSysConfig(
        hUser,                              // 用户句柄
        deviceId.toUtf8().constData(),      // 设备ID
        "SystemInfo",                       // 配置类型
        1020,                              // 通道号
        5000                               // 超时时间(毫秒)
    );

    if (result >= 0) {
        qDebug() << "Request device config sent successfully for device:" << deviceId;
        return true;
    } else {
        qWarning() << "Failed to request device config for device:" << deviceId << "Error:" << result;
        return false;
    }
}

// SDK消息回调函数
int CALLBACK MainWindow::onSDKMessage(XSDK_HANDLE hObject, int nMsgId, int nParam1, int nParam2, int nParam3, const char* szString, void* pObject, int64 lParam, int nSeq, void* pUserData, void* pMsg)
{
    MainWindow* mainWindow = static_cast<MainWindow*>(pUserData);
    if (!mainWindow) {
        qDebug() << "MainWindow pointer is null in callback";
        return 0;
    }

    // 记录详细的SDK消息到日志文件
    QString sdkMessage = QString("SDK Message - ID: %1, Param1: %2, Param2: %3, Param3: %4, String: %5")
                        .arg(nMsgId)
                        .arg(nParam1)
                        .arg(nParam2)
                        .arg(nParam3)
                        .arg(szString ? szString : "null");

    // 使用静态方法记录到日志文件
    MainWindow::logSDKMessage(sdkMessage);

    qDebug() << "SDK Message - ID:" << nMsgId << "Param1:" << nParam1 << "Param2:" << nParam2 << "Param3:" << nParam3 << "String:" << (szString ? szString : "null");

    switch (nMsgId) {
        case 5009: // EACT_MSG_GET_DEV_STATE - 设备状态
            qDebug() << "Device state:" << szString << "Online:" << (nParam1 > 0);
            // 可以在这里处理设备在线状态变化
            break;

        case 5010: // EACT_MSG_GET_DEV_CONFIG - 获取设备配置
            if (nParam1 >= 0) {
                qDebug() << "Get device config success:" << szString;
            } else {
                qDebug() << "Get device config failed:" << szString;
            }
            break;

        case 12001: // ESXSDK_DEV_LOGIN - 设备登录结果
            {
                char szIP[32] = {0};
                const char* szResult = XSDK_GetObjStrAttr(hObject, ATTR_GET_DEV_IP_BY_HANDLE, szIP, 31);
                QString deviceId = QString::fromUtf8(szIP);

                if (nParam1 >= 0) {
                    qDebug() << "=== DEVICE LOGIN SUCCESS ===" << deviceId;
                    mainWindow->setDeviceLoginStatus(deviceId, true); // 线程安全的设置
                    qDebug() << "Device logged in successfully:" << deviceId;

                    // 设备登录成功后，启动报警推送监听
                    qDebug() << "Starting alarm push for device:" << deviceId;
                    mainWindow->startDeviceAlarmPush(deviceId);
                } else {
                    qDebug() << "=== DEVICE LOGIN FAILED ===" << deviceId << "Error:" << nParam1;
                    mainWindow->setDeviceLoginStatus(deviceId, false); // 线程安全的设置
                    qWarning() << "Device login failed:" << deviceId << "Error:" << nParam1;
                }
            }
            break;

        case 12500: // ESXSDK_ON_DEV_STATE - 设备状态变化
            {
                qDebug() << "=== DEVICE STATE CHANGE ===";
                if (nParam1 == 6) { // ESTATE_DEV_Logined
                    qDebug() << "Device state: LOGGED IN";
                } else if (nParam1 == 2) { // ESTATE_DEV_NetDisConnect
                    qDebug() << "Device state: DISCONNECTED";
                } else {
                    qDebug() << "Device state:" << nParam1;
                }
            }
            break;

        case 1504: // EXCMD_ALARM_REQ - 报警消息
            {
                qDebug() << "=== ALARM RECEIVED ===";
                qDebug() << "Alarm Type: EXCMD_ALARM_REQ";
                qDebug() << "Param1:" << nParam1;
                qDebug() << "Param2:" << nParam2;
                qDebug() << "Alarm Data:" << (szString ? szString : "null");

                // 解析报警信息
                if (szString && strlen(szString) > 0) {
                    qDebug() << "=== ALARM DETAILS ===";
                    qDebug() << szString;
                    qDebug() << "=== END ALARM DETAILS ===";

                    // 使用简单的字符串解析来提取Status字段
                    QString alarmJson = QString::fromUtf8(szString);

                    // 查找Status字段
                    int statusIndex = alarmJson.indexOf("\"Status\"");
                    if (statusIndex != -1) {
                        // 查找Status值
                        int colonIndex = alarmJson.indexOf(":", statusIndex);
                        if (colonIndex != -1) {
                            int startQuoteIndex = alarmJson.indexOf("\"", colonIndex);
                            if (startQuoteIndex != -1) {
                                int endQuoteIndex = alarmJson.indexOf("\"", startQuoteIndex + 1);
                                if (endQuoteIndex != -1) {
                                    QString status = alarmJson.mid(startQuoteIndex + 1, endQuoteIndex - startQuoteIndex - 1);

                                    qDebug() << "Parsed alarm status:" << status;

                                    // 只有当Status是"Start"时才触发抓图
                                    if (status == "Start") {
                                        qDebug() << "Alarm status is Start, triggering capture";

                                        // 获取触发报警的设备ID
                                        QString alarmDeviceId;
                                        if (hObject >= 0) {
                                            // 尝试从句柄获取设备IP
                                            char szIP[32] = {0};
                                            const char* szResult = XSDK_GetObjStrAttr(hObject, ATTR_GET_DEV_IP_BY_HANDLE, szIP, 31);
                                            if (szResult && strlen(szIP) > 0) {
                                                alarmDeviceId = QString::fromUtf8(szIP);
                                                qDebug() << "Alarm from device:" << alarmDeviceId;
                                            }
                                        }

                                        // 如果成功获取到设备ID，则进行报警抓图
                                        if (!alarmDeviceId.isEmpty() && mainWindow) {
                                            qDebug() << "Triggering alarm capture for device:" << alarmDeviceId;

                                            // 在主线程中执行抓图操作
                                            QMetaObject::invokeMethod(mainWindow, "performAlarmCapture",
                                                                    Qt::QueuedConnection,
                                                                    Q_ARG(QString, alarmDeviceId));
                                        } else {
                                            qWarning() << "Cannot perform alarm capture: device ID empty or mainWindow null";
                                        }
                                    } else {
                                        qDebug() << "Alarm status is not Start (" << status << "), skipping capture";
                                    }
                                } else {
                                    qWarning() << "Failed to parse Status value from alarm JSON";
                                }
                            } else {
                                qWarning() << "Failed to find Status start quote in alarm JSON";
                            }
                        } else {
                            qWarning() << "Failed to find Status colon in alarm JSON";
                        }
                    } else {
                        qWarning() << "Failed to find Status field in alarm JSON";
                    }
                } else {
                    qDebug() << "No alarm data provided";
                }
            }
            break;

        case 1506: // EXCMD_NET_ALARM_REQ - 网络报警
            {
                qDebug() << "=== NETWORK ALARM RECEIVED ===";
                qDebug() << "Alarm Type: EXCMD_NET_ALARM_REQ";
                qDebug() << "Param1:" << nParam1;
                qDebug() << "Param2:" << nParam2;
                qDebug() << "Alarm Data:" << (szString ? szString : "null");

                if (szString && strlen(szString) > 0) {
                    qDebug() << "=== NETWORK ALARM DETAILS ===";
                    qDebug() << szString;
                    qDebug() << "=== END NETWORK ALARM DETAILS ===";
                }
            }
            break;

        case 15005: // ESXSDK_UPLOAD_DEV_DATA - 上报设备数据（可能包含报警）
            {
                qDebug() << "=== DEVICE DATA UPLOAD ===";
                qDebug() << "Data Type: ESXSDK_UPLOAD_DEV_DATA";
                qDebug() << "Param1:" << nParam1;
                qDebug() << "Param2:" << nParam2;
                qDebug() << "Upload Data:" << (szString ? szString : "null");

                if (szString && strlen(szString) > 0) {
                    // 检查是否包含报警信息
                    QString dataStr = QString::fromUtf8(szString);
                    if (dataStr.contains("alarm", Qt::CaseInsensitive) ||
                        dataStr.contains("alert", Qt::CaseInsensitive)) {
                        qDebug() << "=== ALARM DATA DETECTED ===";
                        qDebug() << szString;
                        qDebug() << "=== END ALARM DATA ===";
                    }
                }
            }
            break;

        case 30007: // EUIMSG_PLAY_SAVE_IMAGE_FILE - 保存图片
            if (nParam1 >= 0) {
                qDebug() << "Image saved successfully:" << szString;
                // 暂时注释掉消息框显示，避免段错误
                // QMetaObject::invokeMethod(mainWindow, [mainWindow, szString]() {
                //     QMessageBox::information(mainWindow, "成功", QString("图片保存成功\n路径: %1").arg(szString ? szString : ""));
                // }, Qt::QueuedConnection);
            } else {
                qDebug() << "Failed to save image:" << szString;
                // 暂时注释掉消息框显示，避免段错误
                // QMetaObject::invokeMethod(mainWindow, [mainWindow]() {
                //     QMessageBox::warning(mainWindow, "错误", "图片保存失败");
                // }, Qt::QueuedConnection);
            }
            break;

        case 12106: // ESXSDK_DEV_SNAP - 设备抓图结果
            {
                qDebug() << "=== DEVICE SNAP RESULT ===";
                qDebug() << "Snap result:" << nParam1;
                qDebug() << "Image data length:" << nParam2;
                qDebug() << "Device ID:" << (szString ? szString : "null");

                if (nParam1 >= 0 && pObject && nParam2 > 0) {
                    qDebug() << "Device snap successful, image data length:" << nParam2;

                    // 尝试从句柄获取设备ID
                    QString deviceId;
                    QString deviceName = "Unknown";

                    if (hObject >= 0) {
                        // 尝试从句柄获取设备IP
                        char szIP[32] = {0};
                        const char* szResult = XSDK_GetObjStrAttr(hObject, ATTR_GET_DEV_IP_BY_HANDLE, szIP, 31);
                        if (szResult && strlen(szIP) > 0) {
                            deviceId = QString::fromUtf8(szIP);
                            qDebug() << "Got device ID from handle:" << deviceId;

                            // 获取设备信息
                            if (mainWindow) {
                                QMap<QString, QString> deviceInfo = mainWindow->getDevice(deviceId);
                                if (!deviceInfo.isEmpty()) {
                                    deviceName = deviceInfo["deviceName"];
                                    qDebug() << "Got device name:" << deviceName;
                                } else {
                                    qWarning() << "Device not found in device list:" << deviceId;
                                }
                            }
                        } else {
                            qWarning() << "Failed to get device ID from handle";
                        }
                    }

                    // 如果还是没有获取到设备信息，使用默认值
                    if (deviceId.isEmpty()) {
                        deviceId = "unknown_device";
                        qWarning() << "Using default device ID";
                    }

                    // 生成保存路径：/captures/alarm/设备名称_设备IP/设备IP_随机字符串_时间戳.jpg
                    // 保持中文字符，只替换文件系统不允许的特殊字符
                    QString safeDeviceId = CaptureManager::toSafeFileName(deviceId);
                    QString safeDeviceName = CaptureManager::toSafeFileName(deviceName);
                    QString timestamp = QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss_zzz");
                    QString randomStr = QString::number(QDateTime::currentMSecsSinceEpoch() % 100000);

                    QString baseDir = QCoreApplication::applicationDirPath() + "/captures/alarm";
                    QString deviceFolderName = QString("%1_%2").arg(safeDeviceName).arg(safeDeviceId);
                    QString deviceAlarmDir = QDir::toNativeSeparators(baseDir + "/" + deviceFolderName);
                    QString fileName = QString("%1_%2_%3.jpg").arg(safeDeviceId).arg(randomStr).arg(timestamp);
                    QString filePath = QDir::toNativeSeparators(deviceAlarmDir + "/" + fileName);

                    qDebug() << "Original device name:" << deviceName;
                    qDebug() << "Safe device name:" << safeDeviceName;
                    qDebug() << "Device folder name:" << deviceFolderName;

                    // 创建设备报警抓图目录
                    QDir dir;
                    if (!dir.exists(deviceAlarmDir)) {
                        if (dir.mkpath(deviceAlarmDir)) {
                            qDebug() << "Created device alarm directory:" << deviceAlarmDir;
                        } else {
                            qWarning() << "Failed to create device alarm directory:" << deviceAlarmDir;
                            filePath.clear();
                        }
                    }

                    qDebug() << "Device alarm capture will be saved to:" << filePath;

                    if (!filePath.isEmpty()) {
                        qDebug() << "Saving alarm image to:" << filePath;

                        // 将图片数据保存到文件
                        QFile file(filePath);
                        if (file.open(QIODevice::WriteOnly)) {
                            qint64 bytesWritten = file.write(static_cast<const char*>(pObject), nParam2);
                            file.close();

                            if (bytesWritten == nParam2) {
                                qDebug() << "Device alarm capture saved successfully:" << filePath;
                                qDebug() << "Alarm image saved to device-specific folder:" << deviceAlarmDir;
                                qDebug() << "File size:" << bytesWritten << "bytes";

                                // 验证文件是否真的存在
                                if (QFile::exists(filePath)) {
                                    qDebug() << "File verification successful, alarm capture completed";

                                    // Perform QR code detection
                                    QRCodeDetector* detector = mainWindow ? mainWindow->getQRCodeDetector() : nullptr;
                                    if (detector && detector->isInitialized()) {
                                        qDebug() << "Starting QR code detection on alarm capture:" << filePath;
                                        QStringList qrResults = detector->detectQRCodes(filePath);

                                        if (!qrResults.isEmpty()) {
                                            qDebug() << "=== QR Code Detection Results ===";
                                            qDebug() << "Device ID:" << deviceId;
                                            qDebug() << "Device Name:" << deviceName;
                                            qDebug() << "Image Path:" << filePath;
                                            qDebug() << "Detected" << qrResults.size() << "QR codes:";

                                            for (int i = 0; i < qrResults.size(); ++i) {
                                                qDebug() << "QR Code" << (i + 1) << ":" << qrResults[i];
                                            }
                                            qDebug() << "=== QR Code Detection Completed ===";
                                        } else {
                                            qDebug() << "No QR codes detected in alarm capture:" << filePath;
                                            if (!detector->getLastError().isEmpty()) {
                                                qWarning() << "QR code detection error:" << detector->getLastError();
                                            }
                                        }
                                    } else {
                                        qWarning() << "QR code detector not initialized, skipping QR code detection";
                                    }
                                } else {
                                    qWarning() << "File verification failed, file does not exist after writing";
                                }
                            } else {
                                qWarning() << "Failed to write complete image data. Expected:" << nParam2 << "Written:" << bytesWritten;
                            }
                        } else {
                            qWarning() << "Failed to open file for writing:" << filePath;
                            qWarning() << "File error:" << file.errorString();
                        }
                    } else {
                        qWarning() << "Failed to generate file path for device snap";
                    }
                } else {
                    qWarning() << "Device snap failed with error:" << nParam1 << "or invalid data";
                }
            }
            break;

        case 30001: // EUIMSG_PLAY_START - 开始播放
            if (nParam1 >= 0) {
                qDebug() << "Play started successfully";
            } else {
                qDebug() << "Failed to start play";
            }
            break;

        case 30002: // EUIMSG_PLAY_STOP - 停止播放
            qDebug() << "Play stopped";
            break;

        case 1001: // EACT_MSG_LOGIN - 登录结果
            if (nParam1 >= 0) {
                qDebug() << "Device login success:" << szString;
            } else {
                qDebug() << "Device login failed:" << szString;
                // 暂时注释掉消息框显示，避免段错误
                // QMetaObject::invokeMethod(mainWindow, [mainWindow]() {
                //     QMessageBox::warning(mainWindow, "错误", "设备登录失败，请检查用户名和密码");
                // }, Qt::QueuedConnection);
            }
            break;

        default:
            qDebug() << "Unhandled SDK message:" << nMsgId;
            break;
    }

    return 0;
}

void MainWindow::onPreviewButtonClicked()
{
    QPushButton* button = qobject_cast<QPushButton*>(sender());
    if (!button) {
        qDebug() << "Invalid sender in onPreviewButtonClicked";
        return;
    }

    QString deviceId = button->property("deviceId").toString();
    qDebug() << "=== Preview button clicked for device:" << deviceId << "===";

    // 检查SDK状态
    qDebug() << "SDK initialized:" << sdkInitialized << "hUser:" << hUser;

    // 首先连接到设备
    QMap<QString, QString> deviceInfo = getDevice(deviceId);
    if (deviceInfo.isEmpty()) {
        qDebug() << "Device info is empty, showing warning";
        qDebug() << "Device information does not exist";
        return;
    }

    QString username = deviceInfo["username"];
    QString password = deviceInfo["password"];
    qDebug() << "Device info - username:" << username << "password:" << password;

    qDebug() << "Attempting to connect to device:" << deviceId;
    if (connectToDevice(deviceId, username, password)) {
        qDebug() << "Device connected successfully, showing preview window";
        qDebug() << "About to call showPreviewWindow()";

        // 显示预览窗口
        showPreviewWindow(deviceId);

        qDebug() << "showPreviewWindow() call completed";
    } else {
        qDebug() << "Device connection failed";
        qDebug() << "Device connection failed, please check device ID, username and password";
    }
    qDebug() << "=== Preview button click handling completed ===";
}

void MainWindow::showPreviewWindow(const QString& deviceId)
{
    qDebug() << "Creating preview window for device:" << deviceId;

    // 获取设备名称
    QString deviceName = "Unknown Device";
    QMap<QString, QString> deviceInfo = getDevice(deviceId);
    if (!deviceInfo.isEmpty()) {
        deviceName = deviceInfo["deviceName"];
    }
    qDebug() << "Device name:" << deviceName;

    try {
        // 创建真正的预览窗口（使用 this 作为父窗口，以便访问MainWindow的功能）
        PreviewWindow* previewWindow = new PreviewWindow(deviceId, deviceName, this);
        if (!previewWindow) {
            QMessageBox::warning(this, "错误", "无法创建预览窗口");
            return;
        }

        previewWindow->setAttribute(Qt::WA_DeleteOnClose);
        previewWindow->setWindowFlags(Qt::Window);  // 确保它是独立窗口
        previewWindow->resize(800, 600);
        previewWindow->show();
        previewWindow->raise();
        previewWindow->activateWindow();

        qDebug() << "Preview window created and shown successfully as independent window";
    } catch (const std::exception& e) {
        qDebug() << "Exception in preview window:" << e.what();
        QMessageBox::critical(this, "错误", QString("创建预览窗口时发生异常: %1").arg(e.what()));
    } catch (...) {
        qDebug() << "Unknown exception in preview window";
        QMessageBox::critical(this, "错误", "创建预览窗口时发生未知异常");
    }
}

void MainWindow::onEditButtonClicked()
{
    QPushButton* btn = qobject_cast<QPushButton*>(sender());
    if (btn) {
        QString deviceId = btn->property("deviceId").toString();
        qDebug() << "Edit button clicked for device:" << deviceId;
        showEditDeviceDialog(deviceId);
    }
}

void MainWindow::showEditDeviceDialog(const QString& deviceId)
{
    // 查找设备信息
    QMap<QString, QString> deviceInfo = getDevice(deviceId);
    if (deviceInfo.isEmpty()) {
        QMessageBox::warning(this, "错误", "未找到设备信息");
        return;
    }

    EditDeviceDialog dialog(deviceInfo, this);
    if (dialog.exec() == QDialog::Accepted) {
        QString newDeviceName = dialog.getDeviceName();
        QString newDeviceId = dialog.getDeviceId();
        QString newUsername = dialog.getUsername();
        QString newPassword = dialog.getPassword();

        // 使用updateDevice方法更新设备信息
        if (updateDevice(deviceId, newDeviceName, newDeviceId, newUsername, newPassword)) {
            QMessageBox::information(this, "成功", QString("设备信息已更新: %1").arg(newDeviceId));
            // 重新显示设备列表页面以更新界面
            showDeviceList();
        } else {
            QMessageBox::warning(this, "警告", "设备ID已存在或更新失败！");
        }
    }
}

bool MainWindow::updateDevice(const QString& originalDeviceId, const QString& deviceName, const QString& deviceId, const QString& username, const QString& password)
{
    // 如果设备ID发生了变化，需要检查新ID是否已存在
    if (originalDeviceId != deviceId) {
        for (const auto& device : deviceList) {
            if (device["deviceId"] == deviceId) {
                return false; // 新设备ID已存在
            }
        }
    }

    // 查找并更新设备信息
    for (auto& device : deviceList) {
        if (device["deviceId"] == originalDeviceId) {
            device["deviceName"] = deviceName;
            device["deviceId"] = deviceId;
            device["username"] = username;
            device["password"] = password;
            device["updateTime"] = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");

            saveDeviceList();  // 立即保存
            return true;
        }
    }

    return false; // 未找到原设备
}

// 设备登录功能
bool MainWindow::loginDevice(const QString& deviceId, const QString& username, const QString& password)
{
    if (!sdkInitialized || hUser < 0) {
        qWarning() << "SDK not initialized, cannot login device:" << deviceId;
        return false;
    }

    // 检查参数有效性
    if (deviceId.isEmpty() || username.isEmpty()) {
        qWarning() << "Invalid device parameters - deviceId:" << deviceId << "username:" << username;
        return false;
    }

    // 检查设备是否已经登录（线程安全）
    if (getDeviceLoginStatus(deviceId)) {
        qDebug() << "Device already logged in:" << deviceId;
        return true;
    }

    qDebug() << "Attempting to login device:" << deviceId << "with username:" << username;

    // 先缓存设备用户名和密码到本地
    int result = XCloudSDK_Device_SetLocalUserNameAndPwd(
        deviceId.toUtf8().constData(),
        username.toUtf8().constData(),
        password.toUtf8().constData()
    );

    if (result != 0) {
        qWarning() << "Failed to set local username and password for device:" << deviceId << "Error:" << result;
        return false;
    }

    // 执行设备登录
    int loginHandle = XCloudSDK_Device_DevLogin(hUser, deviceId.toUtf8().constData());

    if (loginHandle > 0) {
        addDeviceHandle(deviceId, loginHandle); // 线程安全的添加
        qDebug() << "Device login request sent successfully for:" << deviceId << "Handle:" << loginHandle;

        // 设置设备IP属性，用于回调中识别设备
        XSDK_SetObjStrAttr(loginHandle, ATTR_GET_DEV_IP_BY_HANDLE, deviceId.toUtf8().constData());

        return true;
    } else {
        qWarning() << "Failed to send device login request for:" << deviceId << "Error:" << loginHandle;
        return false;
    }
}

// 设备登出功能
void MainWindow::logoutDevice(const QString& deviceId)
{
    if (!sdkInitialized) {
        return;
    }

    if (deviceLoginHandles.contains(deviceId)) {
        qDebug() << "Logging out device:" << deviceId;

        // 先停止报警推送
        stopDeviceAlarmPush(deviceId);

        // 然后登出设备
        XCloudSDK_Device_DevLogout(deviceId.toUtf8().constData());
        deviceLoginHandles.remove(deviceId);
        deviceLoginStatus[deviceId] = false;
    }
}

// 启动报警监听
void MainWindow::startAlarmMonitoring()
{
    if (!sdkInitialized || alarmMonitoringActive) {
        return;
    }

    qDebug() << "Starting alarm monitoring...";
    alarmMonitoringActive = true;

    // 报警监听主要通过SDK回调函数实现
    // 当设备有报警时，会通过onSDKMessage回调函数接收到相关消息
}

// 停止报警监听
void MainWindow::stopAlarmMonitoring()
{
    if (!alarmMonitoringActive) {
        return;
    }

    qDebug() << "Stopping alarm monitoring...";
    alarmMonitoringActive = false;
}

// 编辑设备对话框实现
EditDeviceDialog::EditDeviceDialog(const QMap<QString, QString>& deviceInfo, QWidget *parent) : QDialog(parent)
{
    setWindowTitle("修改设备");
    setFixedSize(450, 460);
    setModal(true);

    // 保存原始设备ID
    originalDeviceId = deviceInfo["deviceId"];

    // 创建主布局
    QVBoxLayout *mainLayout = new QVBoxLayout;
    mainLayout->setContentsMargins(25, 25, 25, 25);
    mainLayout->setSpacing(15);

    // 创建标题
    QLabel *titleLabel = new QLabel("修改设备信息");
    titleLabel->setAlignment(Qt::AlignCenter);
    titleLabel->setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50; margin-bottom: 10px;");

    // 创建表单布局
    QVBoxLayout *formLayout = new QVBoxLayout;
    formLayout->setSpacing(12);

    // 设备名称
    QLabel *deviceNameLabel = new QLabel("设备名称");
    deviceNameLabel->setStyleSheet("font-size: 14px; font-weight: bold; color: #333333;");
    deviceNameEdit = new QLineEdit;
    deviceNameEdit->setText(deviceInfo["deviceName"]);
    deviceNameEdit->setPlaceholderText("请输入设备名称");
    deviceNameEdit->setMinimumHeight(35);
    deviceNameEdit->setStyleSheet("padding: 8px; border: 2px solid #ddd; border-radius: 5px; font-size: 14px; background-color: white;");

    // 设备序列号/IP
    QLabel *deviceIdLabel = new QLabel("设备IP");
    deviceIdLabel->setStyleSheet("font-size: 14px; font-weight: bold; color: #333333;");
    deviceIdEdit = new QLineEdit;
    deviceIdEdit->setText(deviceInfo["deviceId"]);
    deviceIdEdit->setPlaceholderText("例: ************:34567");
    deviceIdEdit->setMinimumHeight(35);
    deviceIdEdit->setStyleSheet("padding: 8px; border: 2px solid #ddd; border-radius: 5px; font-size: 14px; background-color: white;");

    // 用户名
    QLabel *usernameLabel = new QLabel("用户名");
    usernameLabel->setStyleSheet("font-size: 14px; font-weight: bold; color: #333333;");
    usernameEdit = new QLineEdit;
    usernameEdit->setText(deviceInfo["username"]);
    usernameEdit->setPlaceholderText("请输入用户名");
    usernameEdit->setMinimumHeight(35);
    usernameEdit->setStyleSheet("padding: 8px; border: 2px solid #ddd; border-radius: 5px; font-size: 14px; background-color: white;");

    // 密码
    QLabel *passwordLabel = new QLabel("密码");
    passwordLabel->setStyleSheet("font-size: 14px; font-weight: bold; color: #333333;");
    passwordEdit = new QLineEdit;
    passwordEdit->setText(deviceInfo["password"]);
    passwordEdit->setEchoMode(QLineEdit::Password);
    passwordEdit->setMinimumHeight(35);
    passwordEdit->setStyleSheet("padding: 8px; border: 2px solid #ddd; border-radius: 5px; font-size: 14px; background-color: white;");

    // 添加表单项到布局（与AddDeviceDialog保持一致的间距）
    formLayout->addWidget(deviceNameLabel);
    formLayout->addWidget(deviceNameEdit);
    formLayout->addSpacing(8);
    formLayout->addWidget(deviceIdLabel);
    formLayout->addWidget(deviceIdEdit);
    formLayout->addSpacing(8);
    formLayout->addWidget(usernameLabel);
    formLayout->addWidget(usernameEdit);
    formLayout->addSpacing(8);
    formLayout->addWidget(passwordLabel);
    formLayout->addWidget(passwordEdit);

    // 创建按钮布局（与AddDeviceDialog保持一致）
    QHBoxLayout *buttonLayout = new QHBoxLayout;
    buttonLayout->setSpacing(15);

    QPushButton *okButton = new QPushButton("确定");
    QPushButton *cancelButton = new QPushButton("取消");

    okButton->setMinimumHeight(35);
    okButton->setMinimumWidth(80);
    okButton->setStyleSheet("background-color: #4CAF50; color: white; border: none; padding: 8px 15px; font-size: 14px; font-weight: bold; border-radius: 5px;");

    cancelButton->setMinimumHeight(35);
    cancelButton->setMinimumWidth(80);
    cancelButton->setStyleSheet("background-color: #f44336; color: white; border: none; padding: 8px 15px; font-size: 14px; font-weight: bold; border-radius: 5px;");

    buttonLayout->addStretch();
    buttonLayout->addWidget(cancelButton);
    buttonLayout->addWidget(okButton);
    buttonLayout->addStretch();

    // 组装主布局（与AddDeviceDialog保持一致）
    mainLayout->addWidget(titleLabel);
    mainLayout->addSpacing(10);
    mainLayout->addLayout(formLayout);
    mainLayout->addStretch();
    mainLayout->addLayout(buttonLayout);

    setLayout(mainLayout);

    // 设置对话框背景
    setStyleSheet("QDialog { background-color: #f5f5f5; }");

    // 连接信号
    connect(okButton, &QPushButton::clicked, this, &EditDeviceDialog::onOkClicked);
    connect(cancelButton, &QPushButton::clicked, this, &QDialog::reject);

    // 设置焦点到第一个输入框
    deviceNameEdit->setFocus();
}

QString EditDeviceDialog::getDeviceName() const { return deviceNameEdit->text(); }
QString EditDeviceDialog::getDeviceId() const { return deviceIdEdit->text(); }
QString EditDeviceDialog::getUsername() const { return usernameEdit->text(); }
QString EditDeviceDialog::getPassword() const { return passwordEdit->text(); }

void EditDeviceDialog::onOkClicked()
{
    if (deviceNameEdit->text().isEmpty() || deviceIdEdit->text().isEmpty() || usernameEdit->text().isEmpty()) {
        QMessageBox::warning(this, "警告", "请填写设备名称、序列号、用户名");
        return;
    }
    accept();
}

// 启动设备报警推送监听
bool MainWindow::startDeviceAlarmPush(const QString& deviceId)
{
    if (!sdkInitialized || hUser < 0) {
        qWarning() << "SDK not initialized, cannot start alarm push for device:" << deviceId;
        return false;
    }

    if (deviceId.isEmpty()) {
        qWarning() << "Invalid device ID for alarm push";
        return false;
    }

    qDebug() << "Starting alarm listener for device:" << deviceId;

    // 调用SDK的DevSetAlarmListener接口设置设备报警监听器
    int result = XCloudSDK_Device_DevSetAlarmListener(hUser, deviceId.toUtf8().constData());

    if (result >= 0) {
        qDebug() << "Device alarm listener started successfully for:" << deviceId << "Result:" << result;
        return true;
    } else {
        qWarning() << "Failed to start device alarm listener for:" << deviceId << "Error:" << result;
        return false;
    }
}

// 停止设备报警推送监听
void MainWindow::stopDeviceAlarmPush(const QString& deviceId)
{
    if (!sdkInitialized || deviceId.isEmpty()) {
        return;
    }

    qDebug() << "Stopping alarm listener for device:" << deviceId;

    // 调用SDK的DevSetAlarmListener接口，传入0来停止设备报警监听器
    int result = XCloudSDK_Device_DevSetAlarmListener(0, deviceId.toUtf8().constData());

    if (result >= 0) {
        qDebug() << "Device alarm listener stopped successfully for:" << deviceId;
    } else {
        qWarning() << "Failed to stop device alarm listener for:" << deviceId << "Error:" << result;
    }
}

// 报警抓图方法
void MainWindow::performAlarmCapture(const QString& deviceId)
{
    qDebug() << "Performing alarm capture for device:" << deviceId;

    if (deviceId.isEmpty()) {
        qWarning() << "Cannot perform alarm capture: device ID is empty";
        return;
    }

    // 检查设备是否已登录
    if (!deviceLoginStatus.contains(deviceId) || !deviceLoginStatus[deviceId]) {
        qWarning() << "Cannot perform alarm capture: device not logged in:" << deviceId;
        return;
    }

    // 使用设备抓图接口，不需要预览句柄
    qDebug() << "Performing device snap for alarm capture:" << deviceId;
    performDeviceAlarmCapture(deviceId);
}

// 使用设备抓图接口执行报警抓图
void MainWindow::performDeviceAlarmCapture(const QString& deviceId)
{
    qDebug() << "Performing device alarm capture for device:" << deviceId;

    if (!sdkInitialized || hUser < 0) {
        qWarning() << "SDK not initialized for device capture";
        return;
    }

    if (deviceId.isEmpty()) {
        qWarning() << "Device ID is empty for device capture";
        return;
    }

    // 检查设备是否已登录
    if (!deviceLoginStatus.contains(deviceId) || !deviceLoginStatus[deviceId]) {
        qWarning() << "Cannot perform device capture: device not logged in:" << deviceId;
        return;
    }

    // 获取设备信息
    QMap<QString, QString> deviceInfo = getDevice(deviceId);
    QString deviceName = deviceInfo.isEmpty() ? "Unknown" : deviceInfo["deviceName"];

    // 使用CaptureManager生成抓图路径
    QString filePath = CaptureManager::generateCapturePath(deviceId, deviceName, CaptureType::Alarm);
    if (filePath.isEmpty()) {
        qWarning() << "Failed to generate capture path for device:" << deviceId;
        return;
    }

    qDebug() << "Device alarm capture file path:" << filePath;

    // 调用设备抓图接口 XCloudSDK_Device_DevSnap
    // 参数：用户句柄，设备ID，通道号(0)，文件路径
    int result = XCloudSDK_Device_DevSnap(hUser, deviceId.toUtf8().constData(), 0, filePath.toUtf8().constData());

    if (result >= 0) {
        qDebug() << "Device alarm capture request sent successfully for device:" << deviceId;
        qDebug() << "Alarm capture will be saved to:" << filePath;
    } else {
        qWarning() << "Failed to send device alarm capture request for device:" << deviceId << "Error:" << result;
    }
}

// 执行实际的报警抓图
void MainWindow::performActualAlarmCapture(const QString& deviceId)
{
    qDebug() << "Performing actual alarm capture for device:" << deviceId;

    if (!devicePlayHandles.contains(deviceId)) {
        qWarning() << "Cannot capture: no play handle for device:" << deviceId;
        return;
    }

    int playHandle = devicePlayHandles[deviceId];

    // 获取设备信息
    QMap<QString, QString> deviceInfo = getDevice(deviceId);
    QString deviceName = deviceInfo.isEmpty() ? "Unknown" : deviceInfo["deviceName"];

    // 使用CaptureManager执行报警抓图
    bool success = CaptureManager::performCapture(playHandle, deviceId, deviceName, CaptureType::Alarm);

    if (success) {
        qDebug() << "Alarm capture request sent successfully for device:" << deviceId;
    } else {
        qWarning() << "Failed to send alarm capture request for device:" << deviceId;
    }
}

// 为报警抓图启动设备预览
bool MainWindow::startDevicePreview(const QString& deviceId)
{
    qDebug() << "Starting device preview for alarm capture:" << deviceId;

    if (!sdkInitialized || hUser < 0) {
        qWarning() << "SDK not initialized";
        return false;
    }

    if (deviceId.isEmpty()) {
        qWarning() << "Device ID is empty";
        return false;
    }

    // 检查设备是否已登录
    if (!deviceLoginStatus.contains(deviceId) || !deviceLoginStatus[deviceId]) {
        qWarning() << "Device not logged in:" << deviceId;
        return false;
    }

    // 检查是否已经有播放句柄
    if (devicePlayHandles.contains(deviceId)) {
        qDebug() << "Device already has play handle:" << deviceId;
        return true;
    }

    // 启动实时预览（用于报警抓图，不需要显示窗口）
    int playHandle = XCloudSDK_Device_MediaRealPlay(hUser, deviceId.toUtf8().constData(), 0, 0, (LP_WND_OBJ)0);

    if (playHandle >= 0) {
        devicePlayHandles[deviceId] = playHandle;
        qDebug() << "Started preview for alarm capture, device:" << deviceId << "handle:" << playHandle;
        return true;
    } else {
        qWarning() << "Failed to start preview for alarm capture, device:" << deviceId << "error:" << playHandle;
        return false;
    }
}

// 测试二维码检测器
void MainWindow::testQRCodeDetector()
{
    qDebug() << "=== Testing QR Code Detector ===";

    if (!qrCodeDetector) {
        qWarning() << "QR code detector is null!";
        return;
    }

    qDebug() << "QR code detector exists";
    qDebug() << "Is initialized:" << qrCodeDetector->isInitialized();

    if (!qrCodeDetector->isInitialized()) {
        qWarning() << "QR code detector not initialized!";
        qWarning() << "Last error:" << qrCodeDetector->getLastError();

        // 尝试重新初始化
        QString modelPath = QCoreApplication::applicationDirPath() + "/models";
        qDebug() << "Attempting to reinitialize with path:" << modelPath;

        // 检查模型文件
        QStringList modelFiles = {"detect.prototxt", "detect.caffemodel", "sr.prototxt", "sr.caffemodel"};
        for (const QString& file : modelFiles) {
            QString fullPath = modelPath + "/" + file;
            bool exists = QFile::exists(fullPath);
            qDebug() << "Model file" << file << "exists:" << exists;
            if (exists) {
                QFileInfo info(fullPath);
                qDebug() << "  Size:" << info.size() << "bytes";
            }
        }

        if (qrCodeDetector->initialize(modelPath)) {
            qDebug() << "Reinitialization successful!";
        } else {
            qWarning() << "Reinitialization failed:" << qrCodeDetector->getLastError();
        }
    } else {
        qDebug() << "QR code detector is properly initialized!";
    }

    qDebug() << "=== QR Code Detector Test Complete ===";
}

// 并发和资源管理实现
void MainWindow::initializeResourceManagement()
{
    qDebug() << "Initializing resource management for concurrent operations";

    // 创建线程池
    deviceThreadPool = new QThreadPool(this);
    deviceThreadPool->setMaxThreadCount(qMin(QThread::idealThreadCount(), 8)); // 限制最大线程数

    // 创建信号量
    loginSemaphore = new QSemaphore(MAX_CONCURRENT_LOGINS);
    previewSemaphore = new QSemaphore(MAX_CONCURRENT_PREVIEWS);

    // 创建设备状态检查定时器
    deviceStatusTimer = new QTimer(this);
    connect(deviceStatusTimer, &QTimer::timeout, this, &MainWindow::checkDeviceStatus);
    deviceStatusTimer->start(30000); // 每30秒检查一次设备状态

    qDebug() << "Resource management initialized - Max threads:" << deviceThreadPool->maxThreadCount()
             << "Max concurrent logins:" << MAX_CONCURRENT_LOGINS
             << "Max concurrent previews:" << MAX_CONCURRENT_PREVIEWS;
}

void MainWindow::cleanupResourceManagement()
{
    qDebug() << "Cleaning up resource management";

    if (deviceStatusTimer) {
        deviceStatusTimer->stop();
    }

    if (deviceThreadPool) {
        deviceThreadPool->waitForDone(5000); // 等待5秒让任务完成
    }

    delete loginSemaphore;
    delete previewSemaphore;
    loginSemaphore = nullptr;
    previewSemaphore = nullptr;
}

// 线程安全的设备状态管理
void MainWindow::setDeviceLoginStatus(const QString& deviceId, bool status)
{
    QMutexLocker locker(&deviceMapMutex);
    deviceLoginStatus[deviceId] = status;
}

bool MainWindow::getDeviceLoginStatus(const QString& deviceId)
{
    QMutexLocker locker(&deviceMapMutex);
    return deviceLoginStatus.value(deviceId, false);
}

void MainWindow::addDeviceHandle(const QString& deviceId, int handle)
{
    QMutexLocker locker(&deviceMapMutex);
    deviceLoginHandles[deviceId] = handle;
}

void MainWindow::removeDeviceHandle(const QString& deviceId)
{
    QMutexLocker locker(&deviceMapMutex);
    deviceLoginHandles.remove(deviceId);
}

int MainWindow::getDeviceHandle(const QString& deviceId)
{
    QMutexLocker locker(&deviceMapMutex);
    return deviceLoginHandles.value(deviceId, -1);
}

// 并发登录设备
void MainWindow::loginDevicesConcurrently()
{
    qDebug() << "Starting concurrent device login for" << deviceList.size() << "devices";

    if (!sdkInitialized || hUser < 0) {
        qWarning() << "SDK not initialized, cannot login devices";
        return;
    }

    // 创建登录任务
    class DeviceLoginTask : public QRunnable
    {
    public:
        DeviceLoginTask(MainWindow* mainWindow, const QString& deviceId,
                       const QString& username, const QString& password)
            : m_mainWindow(mainWindow), m_deviceId(deviceId),
              m_username(username), m_password(password) {}

        void run() override
        {
            if (!m_mainWindow || !m_mainWindow->loginSemaphore) {
                return;
            }

            // 获取登录信号量
            m_mainWindow->loginSemaphore->acquire();

            qDebug() << "Starting login for device:" << m_deviceId;

            // 执行登录
            bool success = m_mainWindow->loginDevice(m_deviceId, m_username, m_password);

            if (success) {
                qDebug() << "Device login successful:" << m_deviceId;
            } else {
                qWarning() << "Device login failed:" << m_deviceId;
            }

            // 释放信号量
            m_mainWindow->loginSemaphore->release();
        }

    private:
        MainWindow* m_mainWindow;
        QString m_deviceId;
        QString m_username;
        QString m_password;
    };

    // 为每个设备创建登录任务
    for (const auto& device : deviceList) {
        QString deviceId = device["deviceId"];
        QString username = device["username"];
        QString password = device["password"];

        if (!deviceId.isEmpty() && !username.isEmpty()) {
            DeviceLoginTask* task = new DeviceLoginTask(this, deviceId, username, password);
            task->setAutoDelete(true);
            deviceThreadPool->start(task);
        }
    }
}

// 设备状态检查
void MainWindow::checkDeviceStatus()
{
    QMutexLocker locker(&deviceMapMutex);

    qDebug() << "Checking status of" << deviceLoginHandles.size() << "devices";

    QStringList disconnectedDevices;

    // 检查每个已登录设备的状态
    for (auto it = deviceLoginHandles.begin(); it != deviceLoginHandles.end(); ++it) {
        const QString& deviceId = it.key();
        int handle = it.value();

        // 这里可以添加设备心跳检查或状态查询
        // 如果设备断开连接，添加到断开列表
        // 示例：检查设备是否仍然在线
        if (handle <= 0) {
            disconnectedDevices.append(deviceId);
        }
    }

    // 处理断开的设备
    for (const QString& deviceId : disconnectedDevices) {
        qWarning() << "Device disconnected, attempting reconnection:" << deviceId;

        // 查找设备信息并尝试重连
        for (const auto& device : deviceList) {
            if (device["deviceId"] == deviceId) {
                QString username = device["username"];
                QString password = device["password"];

                // 异步重连
                QTimer::singleShot(1000, this, [this, deviceId, username, password]() {
                    loginDevice(deviceId, username, password);
                });
                break;
            }
        }
    }
}

// 静态方法：记录SDK消息到日志文件
void MainWindow::logSDKMessage(const QString& message)
{
    // 声明外部函数
    extern void logSDKOutput(const QString& message);
    logSDKOutput(message);
}
