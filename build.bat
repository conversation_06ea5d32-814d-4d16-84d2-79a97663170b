@echo off
REM Windows构建脚本
REM 自动下载OpenCV并构建IDCamera项目

echo ========================================
echo IDCamera Windows构建脚本
echo ========================================

REM 检查Python是否可用
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: Python未安装或不在PATH中
    echo 请安装Python 3.6+并确保添加到PATH
    pause
    exit /b 1
)

REM 检查CMake是否可用
cmake --version >nul 2>&1
if errorlevel 1 (
    echo 错误: CMake未安装或不在PATH中
    echo 请安装CMake 3.16+并确保添加到PATH
    pause
    exit /b 1
)

REM 运行构建脚本
echo 开始构建...
python scripts\build.py

if errorlevel 1 (
    echo 构建失败!
    pause
    exit /b 1
)

echo.
echo ========================================
echo 构建完成!
echo ========================================
echo 可执行文件位置: build\Release\idcamera.exe
echo.

pause
