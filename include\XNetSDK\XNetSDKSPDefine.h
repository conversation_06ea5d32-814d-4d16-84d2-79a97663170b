﻿#pragma once
#include "XNetSDK/XNetSDKDefine.h"

/*======================车位上报信息========================*/
// 车位状态
enum E_PGS_SPACE_STATE
{
    E_PGS_SPACE_STATE_FIXED_Y = 0,         // 专有车位占用
    E_PGS_SPACE_STATE_FIXED_N = 1,         // 专有车位未占用
    E_PGS_SPACE_STATE_GENERAL_Y = 2,       // 普通车位占用
    E_PGS_SPACE_STATE_GENERAL_N = 3,       // 普通车位未占用
    E_PGS_SPACE_STATE_ILLEGAL_PARKING = 4, // 非法停车
    E_PGS_SPACE_STATE_ILLEGAL_LEFT = 5,    // 被左方车占用
    E_PGS_SPACE_STATE_ILLEGAL_RIGHT = 6,   // 被右方车占用
    E_PGS_SPACE_STATE_ALL = 7
};

// 坐标信息
typedef struct SXSDK_PgsRect
{
    int iLftX;
    int iTopY;
    int iRgnWid;
    int iRgnHgt;
} SXSDK_PgsRect;

// 车位具体信息
typedef struct SXSDK_CarInfo
{
    int iState;          // 车位状态:参考枚举 E_PGS_SPACE_STATE
    char pPlateCode[32]; // 车牌号码，尾部有字符串结束符"\0"
                         // ,字符编码格式为Utf8,有车时为车牌号，无车时为"无车",有车但未检测到车牌时"无牌车"
    char pPlateColor[4]; // 车牌颜色，黄蓝黑白等,"无"代表未知，字符串结束符"\0"，字符编码格式为Utf8
    SXSDK_PgsRect rect; // 检测框的坐标信息
} SXSDK_CarInfo;

// 车位检测目标结果
typedef struct SXSDK_PgsTargetInfo
{
    int iChannel; // 通道号
    SXSDK_CarInfo carInfo
        [3]; // 每个相机检测三个车位，以相机拍摄为当前视角，从左到右，对应关系为(carInfo[0]--左)、(carInfo[1]--中)、(carInfo[2]--右)
} SXSDK_PgsTargetInfo;

// 车位检测结果
typedef struct SXSDK_IA_PGS_REC_S // 车位检测
{
    SXSDK_IA_COMM_RES_S comm;     // 车位基本信息
    void* pTargetInfo;            // 车位具体信息
} SXSDK_IA_PGS_REC_S;
