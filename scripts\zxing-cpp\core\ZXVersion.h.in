/*
* Copyright 2019 Nu-book Inc.
* Copyright 2023 <PERSON>
*/
// SPDX-License-Identifier: Apache-2.0

#pragma once

// Version numbering
#define ZXING_VERSION_MAJOR @PROJECT_VERSION_MAJOR@
#define ZXING_VERSION_MINOR @PROJECT_VERSION_MINOR@
#define ZXING_VERSION_PATCH @PROJECT_VERSION_PATCH@

namespace ZXing {

constexpr const char* ZXING_VERSION_STR = "@PROJECT_VERSION_MAJOR@.@PROJECT_VERSION_MINOR@.@PROJECT_VERSION_PATCH@";

}
