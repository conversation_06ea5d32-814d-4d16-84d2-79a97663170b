#!/bin/bash
# macOS/Linux构建脚本
# 自动下载OpenCV并构建IDCamera项目

set -e  # 遇到错误时退出

echo "========================================"
echo "IDCamera macOS/Linux构建脚本"
echo "========================================"

# 检查Python是否可用
if ! command -v python3 &> /dev/null; then
    echo "错误: Python3未安装"
    echo "请安装Python 3.6+"
    exit 1
fi

# 检查CMake是否可用
if ! command -v cmake &> /dev/null; then
    echo "错误: CMake未安装"
    echo "macOS: brew install cmake"
    echo "Ubuntu: sudo apt install cmake"
    exit 1
fi

# 检查Qt是否可用
if ! command -v qmake &> /dev/null && ! command -v qmake6 &> /dev/null; then
    echo "警告: Qt未检测到"
    echo "macOS: brew install qt"
    echo "Ubuntu: sudo apt install qt5-default qtbase5-dev"
    echo "继续构建..."
fi

# macOS特殊处理：检查OpenCV是否已安装
if [[ "$OSTYPE" == "darwin"* ]]; then
    OPENCV_DIR="third_party/opencv"
    if [ ! -d "$OPENCV_DIR" ] || [ ! -f "$OPENCV_DIR/include/opencv2/opencv.hpp" ]; then
        echo "========================================"
        echo "检测到macOS系统，需要先安装OpenCV"
        echo "========================================"
        echo "运行OpenCV安装脚本..."
        ./scripts/setup_macos.sh

        if [ $? -ne 0 ]; then
            echo "OpenCV安装失败，请手动安装"
            echo "参考: BUILD_INSTRUCTIONS.md"
            exit 1
        fi
    else
        echo "✅ OpenCV已安装"
    fi
fi

# 运行构建脚本
echo "开始构建..."
python3 scripts/build.py

echo ""
echo "========================================"
echo "构建完成!"
echo "========================================"
echo "可执行文件位置: build/idcamera"
echo ""
