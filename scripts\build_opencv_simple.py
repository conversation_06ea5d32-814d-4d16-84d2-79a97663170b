#!/usr/bin/env python3
"""
简化的OpenCV编译脚本，专门用于重新编译包含微信二维码模块的OpenCV
"""

import os
import sys
import subprocess
from pathlib import Path

# 配置
PROJECT_ROOT = Path(__file__).parent.parent
OPENCV_DIR = PROJECT_ROOT / "third_party" / "opencv"
OPENCV_VERSION = "4.8.0"

def main():
    print("简化OpenCV编译脚本 - 包含微信二维码模块")
    print("=" * 60)
    
    # 检查源码目录
    opencv_src_dir = OPENCV_DIR / f"opencv-{OPENCV_VERSION}"
    contrib_src_dir = OPENCV_DIR / f"opencv_contrib-{OPENCV_VERSION}"
    
    if not opencv_src_dir.exists():
        print(f"错误: OpenCV源码目录不存在: {opencv_src_dir}")
        return 1
        
    if not contrib_src_dir.exists():
        print(f"错误: OpenCV Contrib源码目录不存在: {contrib_src_dir}")
        return 1
    
    # 创建构建目录
    build_dir = opencv_src_dir / "build"
    build_dir.mkdir(exist_ok=True)
    
    # 查找工具
    mingw_path = Path("D:/Qt/Tools/mingw1310_64/bin")
    cmake_path = Path("D:/Qt/Tools/CMake_64/bin/cmake.exe")
    
    if not mingw_path.exists():
        print(f"错误: MinGW路径不存在: {mingw_path}")
        return 1
        
    if not cmake_path.exists():
        print(f"错误: CMake路径不存在: {cmake_path}")
        return 1
    
    print(f"OpenCV源码: {opencv_src_dir}")
    print(f"Contrib源码: {contrib_src_dir}")
    print(f"构建目录: {build_dir}")
    print(f"MinGW: {mingw_path}")
    print(f"CMake: {cmake_path}")
    
    # 配置CMake
    print("\n配置CMake...")
    
    # 使用正斜杠路径
    mingw_path_str = str(mingw_path).replace('\\', '/')
    opencv_install_path = str(OPENCV_DIR / "opencv").replace('\\', '/')
    contrib_modules_path = str(contrib_src_dir / "modules").replace('\\', '/')
    
    cmake_cmd = [
        str(cmake_path),
        "-G", "MinGW Makefiles",
        f"-DCMAKE_C_COMPILER={mingw_path_str}/gcc.exe",
        f"-DCMAKE_CXX_COMPILER={mingw_path_str}/g++.exe",
        f"-DCMAKE_MAKE_PROGRAM={mingw_path_str}/mingw32-make.exe",
        f"-DCMAKE_INSTALL_PREFIX={opencv_install_path}",
        f"-DOPENCV_EXTRA_MODULES_PATH={contrib_modules_path}",
        
        # 启用微信二维码模块
        "-DBUILD_opencv_wechat_qrcode=ON",

        # 基本配置
        "-DCMAKE_BUILD_TYPE=Release",
        "-DBUILD_SHARED_LIBS=OFF",

        # 禁用不需要的模块以加快编译
        "-DBUILD_EXAMPLES=OFF",
        "-DBUILD_TESTS=OFF",
        "-DBUILD_PERF_TESTS=OFF",
        "-DBUILD_opencv_python2=OFF",
        "-DBUILD_opencv_python3=OFF",
        "-DBUILD_opencv_java=OFF",
        "-DBUILD_DOCS=OFF",

        # 禁用有问题的videoio后端（MinGW兼容性问题）
        "-DWITH_MSMF=OFF",           # Microsoft Media Foundation
        "-DWITH_DSHOW=OFF",          # DirectShow
        "-DWITH_DIRECTX=OFF",        # DirectX
        "-DWITH_OBSENSOR=OFF",       # ObSensor（有COM模板问题）

        # 禁用其他可能有问题的依赖
        "-DWITH_QT=OFF",
        "-DWITH_GTK=OFF",
        "-DWITH_OPENGL=OFF",
        "-DWITH_OPENCL=OFF",
        "-DWITH_IPP=OFF",
        "-DWITH_TBB=OFF",
        "-DWITH_PROTOBUF=OFF",       # 禁用protobuf以避免警告

        # 禁用一些contrib模块中可能有问题的部分
        "-DBUILD_opencv_ovis=OFF",   # 需要OpenGL
        "-DBUILD_opencv_viz=OFF",    # 需要VTK
        
        # 禁用警告以加快编译
        "-DCMAKE_CXX_FLAGS=-w",
        "-DCMAKE_C_FLAGS=-w",
        
        ".."
    ]
    
    print("CMake命令:")
    for arg in cmake_cmd:
        print(f"  {arg}")
    
    try:
        print("\n执行CMake配置...")
        result = subprocess.run(cmake_cmd, cwd=build_dir, check=True, 
                              capture_output=True, text=True)
        print("✅ CMake配置成功")
    except subprocess.CalledProcessError as e:
        print(f"❌ CMake配置失败: {e}")
        print(f"错误输出:\n{e.stderr}")
        return 1
    
    # 编译
    print("\n开始编译OpenCV...")
    print("这可能需要很长时间，请耐心等待...")
    
    make_cmd = [str(mingw_path / "mingw32-make.exe"), "-j2"]  # 使用2个线程以避免内存不足
    
    try:
        print("执行编译...")
        result = subprocess.run(make_cmd, cwd=build_dir, check=True)
        print("✅ 编译成功")
    except subprocess.CalledProcessError as e:
        print(f"❌ 编译失败: {e}")
        return 1
    
    # 安装
    print("\n安装OpenCV...")
    install_cmd = [str(mingw_path / "mingw32-make.exe"), "install"]
    
    try:
        result = subprocess.run(install_cmd, cwd=build_dir, check=True)
        print("✅ 安装成功")
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装失败: {e}")
        return 1
    
    print("\n🎉 OpenCV编译完成!")
    print(f"安装路径: {OPENCV_DIR / 'opencv'}")
    print("微信二维码模块已包含在编译中")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
