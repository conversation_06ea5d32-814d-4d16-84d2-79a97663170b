#!/usr/bin/env python3
"""
OpenCV安装测试脚本
验证下载的OpenCV库是否包含所需的模块
"""

import sys
import platform
from pathlib import Path

PROJECT_ROOT = Path(__file__).parent.parent
OPENCV_DIR = PROJECT_ROOT / "third_party" / "opencv"

def test_opencv_files():
    """测试OpenCV文件是否存在"""
    print("测试OpenCV文件结构...")
    
    system = platform.system()
    
    if system == "Windows":
        # Windows文件检查
        opencv_root = OPENCV_DIR / "opencv"
        required_files = [
            opencv_root / "build" / "include" / "opencv2" / "opencv.hpp",
            opencv_root / "build" / "include" / "opencv2" / "wechat_qrcode.hpp",
            opencv_root / "build" / "x64" / "vc15" / "lib",
            opencv_root / "build" / "x64" / "vc15" / "bin"
        ]
        
        # 检查库文件
        lib_dir = opencv_root / "build" / "x64" / "vc15" / "lib"
        if lib_dir.exists():
            lib_files = list(lib_dir.glob("opencv_core*.lib"))
            if not lib_files:
                print("❌ 未找到opencv_core库文件")
                return False
            else:
                print(f"✅ 找到核心库文件: {lib_files[0].name}")
        
    elif system == "Darwin":
        # macOS文件检查
        required_files = [
            OPENCV_DIR / "include" / "opencv2" / "opencv.hpp",
            OPENCV_DIR / "lib"
        ]
        
        # 检查库文件
        lib_dir = OPENCV_DIR / "lib"
        if lib_dir.exists():
            # 查找动态库文件
            dylib_files = list(lib_dir.glob("libopencv_core*.dylib"))
            if not dylib_files:
                # 查找静态库文件
                a_files = list(lib_dir.glob("libopencv_core*.a"))
                if not a_files:
                    print("❌ 未找到opencv_core库文件")
                    return False
                else:
                    print(f"✅ 找到静态库文件: {a_files[0].name}")
            else:
                print(f"✅ 找到动态库文件: {dylib_files[0].name}")
    
    else:
        print(f"❌ 不支持的操作系统: {system}")
        return False
    
    # 检查必需文件
    missing_files = []
    for file_path in required_files:
        if not file_path.exists():
            missing_files.append(file_path)
        else:
            print(f"✅ {file_path.relative_to(PROJECT_ROOT)}")
    
    if missing_files:
        print("\n❌ 缺少以下文件:")
        for file_path in missing_files:
            print(f"   {file_path.relative_to(PROJECT_ROOT)}")
        return False
    
    return True

def test_opencv_modules():
    """测试OpenCV模块是否可用"""
    print("\n测试OpenCV模块...")
    
    system = platform.system()
    
    if system == "Windows":
        lib_dir = OPENCV_DIR / "opencv" / "build" / "x64" / "vc15" / "lib"
    else:
        lib_dir = OPENCV_DIR / "lib"
    
    if not lib_dir.exists():
        print(f"❌ 库目录不存在: {lib_dir}")
        return False
    
    # 检查必需的模块
    required_modules = [
        "opencv_core",
        "opencv_imgproc", 
        "opencv_imgcodecs",
        "opencv_objdetect"
    ]
    
    optional_modules = [
        "opencv_wechat_qrcode"
    ]
    
    found_modules = []
    missing_modules = []
    
    for module in required_modules + optional_modules:
        if system == "Windows":
            pattern = f"{module}*.lib"
        else:
            pattern = f"lib{module}*"
        
        files = list(lib_dir.glob(pattern))
        if files:
            found_modules.append(module)
            print(f"✅ {module}: {files[0].name}")
        else:
            missing_modules.append(module)
            if module in required_modules:
                print(f"❌ {module}: 未找到")
            else:
                print(f"⚠️  {module}: 未找到（可选模块）")
    
    # 检查是否有必需模块缺失
    missing_required = [m for m in missing_modules if m in required_modules]
    if missing_required:
        print(f"\n❌ 缺少必需模块: {', '.join(missing_required)}")
        return False
    
    return True

def test_cmake_config():
    """测试CMake配置"""
    print("\n测试CMake配置...")
    
    cmake_file = PROJECT_ROOT / "CMakeLists.txt"
    if not cmake_file.exists():
        print("❌ CMakeLists.txt不存在")
        return False
    
    # 读取CMakeLists.txt内容
    content = cmake_file.read_text(encoding='utf-8')
    
    # 检查是否包含本地OpenCV配置
    if "OPENCV_LOCAL_DIR" in content:
        print("✅ CMakeLists.txt已配置使用本地OpenCV")
    else:
        print("❌ CMakeLists.txt未配置本地OpenCV")
        return False
    
    # 检查是否移除了find_package(OpenCV REQUIRED)
    if "find_package(OpenCV REQUIRED)" in content:
        print("⚠️  CMakeLists.txt仍包含find_package(OpenCV REQUIRED)")
        print("   这可能导致使用系统OpenCV而不是本地版本")
    else:
        print("✅ 已移除系统OpenCV依赖")
    
    return True

def main():
    """主函数"""
    print("OpenCV安装测试")
    print("=" * 50)
    print(f"项目根目录: {PROJECT_ROOT}")
    print(f"OpenCV目录: {OPENCV_DIR}")
    print(f"平台: {platform.system()} {platform.machine()}")
    print()
    
    # 检查OpenCV目录是否存在
    if not OPENCV_DIR.exists():
        print("❌ OpenCV目录不存在")
        print("请先运行: python scripts/download_opencv.py")
        return 1
    
    success = True
    
    # 测试文件结构
    if not test_opencv_files():
        success = False
    
    # 测试模块
    if not test_opencv_modules():
        success = False
    
    # 测试CMake配置
    if not test_cmake_config():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("✅ 所有测试通过！OpenCV配置正确")
        print("现在可以运行: python scripts/build.py")
        return 0
    else:
        print("❌ 测试失败，请检查OpenCV安装")
        print("尝试重新运行: python scripts/download_opencv.py")
        return 1

if __name__ == "__main__":
    sys.exit(main())
