﻿/*********************************************************************************
 *Description:
 *History:
 Date:    2024.03.28
 Action：Create
 **********************************************************************************/
#pragma once
#ifndef XCPLAY_H
#define XCPLAY_H
#include "XBasic/XSDKPublic.h"

#ifdef __cplusplus
extern "C"
{
#endif
    /**
     * @brief 打开/关闭声音
     * @param nPlayHandle 播放句柄
     * @param nVolume 100:打开音频;0:关闭音频
     * @param nSeq 自定义值
     * @return >=0:成功 <0:失败
     */
    XSDK_API int CALLBACK XCloudSDK_Play_PlaySound(int nPlayHandle, int nVolume, int nSeq = 0);

    /**
     * @brief 录制音频
     * @param hUser 用户句柄
     * @param nBitsPerSample 采样位深度
     * @param nSamplesPerSec 采样率
     * @param nLength 音频数据包长度
     * @return >0音频采集句柄，<=0失败
     */
    XSDK_API int CALLBACK XCloudSDK_Play_StartCaptureAudio(int hUser,
                                                           int nBitsPerSample,
                                                           int nSamplesPerSec,
                                                           int nLength);

    /**
     * @brief 停止音频录制
     * @param nAudioPlayer 音频采集句柄
     * @return >=0成功，<0失败
     */
    XSDK_API int CALLBACK XCloudSDK_Play_StopCaptureAudio(int nAudioPlayer);

    /**
     * @brief 视频文件播放
     * @param hUser 用户句柄
     * @param szFileName 文件名
     * @param hWnd 窗口句柄（类型为HWND）
     * @param nDecodeType 解码类型
     * @param pParam 扩展参数
     * @return 播放句柄
     */
    XSDK_API int CALLBACK
    XCloudSDK_Play_OpenFilePlay(int hUser, const char* szFileName, LP_WND_OBJ hWnd, int nDecodeType, void* pParam);

    /**
     * @brief 实时预览--抓图
     * @param nPlayHandle 播放句柄--实时预览接口返回值
     * @param szFileName 图片保存路径
     * @return
     * @retval id: 30007 = EUIMSG_PLAY_SAVE_IMAGE_FILE, 本地保存图片
     * @retval param1: >=0成功，否则失败
     */
    XSDK_API int CALLBACK XCloudSDK_Play_MediaSnapImage(int nPlayHandle, const char* szFileName);

    /**
     * @brief 实时预览--抓图(多通道)
     * @param nPlayHandle 播放句柄--实时预览接口返回值
     * @param szFileName 图片保存路径
     * @param nChannel_index 通道号
     * @param param 参数
     *          1
     *   +-------------+
     *   |             |
     * 0 |             | 1
     *   |             |
     *   |             |
     *   +-------------+
     *          0
     * {
     *    "crop":          ///< 【可选】裁剪区域, 范围[0, 1]
     *    {
     *        "top": 1,【默认值:1】
     *        "bottom": 0,【默认值:0】
     *        "left": 0【默认值:0】
     *        "right": 1,【默认值:1】
     *    }
     * }
     * @return
     * @retval id: 30007 = EUIMSG_PLAY_SAVE_IMAGE_FILE, 本地保存图片
     * @retval param1: >=0成功，否则失败
     */
    XSDK_API int CALLBACK XCloudSDK_Play_MediaSnapImageEx(int nPlayHandle,
                                                          const char* szFileName,
                                                          int nChannel_index,
                                                          const char* param = "");

    /**
     * @brief 开始录制预览画面
     * @param nPlayeHandle 预览接口返回句柄
     * @param szFileName 文件名----支持MP4格式，后缀为.mp4
     * @return >=0:成功;<0:失败
     * @retval id: 30011 = EUIMSG_RECORD_START, 开始录制
     * @retval param1: >=0成功，否则失败
     */
    XSDK_API int CALLBACK XCloudSDK_Play_StartRecord(int nPlayHandle, const char* szFileName);

    /**
     * @brief 开始录制预览画面(多窗口)
     * @param nPlayeHandle 预览接口返回句柄
     * @param szFileName 文件名----支持MP4格式，后缀为.mp4
     * @param nChannel_index 通道号
     * @return >=0:成功;<0:失败
     * @retval id: 30011 = EUIMSG_RECORD_START, 开始录制
     * @retval param1: >=0成功，否则失败
     */
    XSDK_API int CALLBACK XCloudSDK_Play_StartRecordEx(int nPlayHandle, const char* szFileName, int nChannel_index);

    /**
     * @brief 停止录制预览画面(多窗口)
     * @param nPlayHandle 预览接口返回句柄
     * @return >=0:成功;<0:失败
     * @retval id: 30012 = EUIMSG_RECORD_STOP, 停止录制
     * @retval param1: >=0成功，否则失败
     */
    XSDK_API int CALLBACK XCloudSDK_Play_StopRecord(int nPlayHandle);

    /**
     * @brief 停止录制预览画面(多窗口)
     * @param nPlayHandle 预览接口返回句柄
     * @param nChannel_index 通道号
     * @return >=0:成功;<0:失败
     * @retval id: 30012 = EUIMSG_RECORD_STOP, 停止录制
     * @retval param1: >=0成功，否则失败
     */
    XSDK_API int CALLBACK XCloudSDK_Play_StopRecordEx(int nPlayHandle, int nChannel_index);

    /**
     * @brief 播放URL地址
     * @param hUser 用户句柄
     * @param szUrl 媒体URL
     * @param szUrlExInfo Url拓展参数（例如平台infoEx中的信息)---可不传
     * @param hWnd 窗口句柄
     * @param bFileStream 是否是文件流
     * @param pParam 扩展参数
     * @return >0播放句柄，<=0失败
     * @retval id: 30000 = EUIMSG_PLAY_START, 开始URL播放  param1: >=0 成功，否则失败
     * @retval id: 30003 = EUIMSG_START_BUFFER_DATA, 正在缓冲数据
     * @retval id: 30004 = EUIMSG_END_BUFFER_DATA, 缓冲结束，开始播放
     * @retval id: 30006 = EUIMSG_PLAY_INFO, 播放信息 param1:开始时间戳  param2:正在播放时间戳
     * param3:结束播放时间戳（单位均为秒）
     * @retval id: 30002 = EUIMSG_PLAY_STOP, 播放结束
     */
    XSDK_API int CALLBACK XCloudSDK_Play_MediaPlayByUrl(int hUser,
                                                        const char* szUrl,
                                                        const char* szUrlExInfo,
                                                        LP_WND_OBJ hWnd,
                                                        bool bFileStream,
                                                        void* pParam);

    /**
     * @brief 下载URL地址对应的视频
     * @param hUser 用户句柄
     * @param szUrl 媒体URL
     * @param pUrlExInfo Url拓展参数（例如平台infoEx中的信息)---可不传
     * @param szFileName 要保存的文件名
     * @return
     * @retval id: 30013 = EUIMSG_ON_FILE_DOWNLOAD，下载URL视频
     * @retval param1为1---正在下载---param2对应下载进度
     * @retval param1为2---转换文件进度（MP4文件）---param2对应转换进度
     * @retval param1为10---下载完成---param2大于等于0表示下载成功，param2小于0表示下载失败
     */
    XSDK_API int CALLBACK XCloudSDK_Play_MediaDownloadByUrl(int hUser,
                                                            const char* szUrl,
                                                            const char* szUrlExInfo,
                                                            const char* szFileName);

    /**
     * @brief 关闭播放
     * @param nPlayHandle
     * @return
     */
    XSDK_API void CALLBACK XCloudSDK_Play_StopUrlPlay(int nPlayHandle);

    /**
     * @brief 设置窗口
     * @param nPlayHandle 播放句柄
     * @param pParam 安卓环境下使用
     * @param hWnd 窗口句柄
     * @return >=0成功，<=0失败
     */
    XSDK_API int CALLBACK XCloudSDK_Play_SetPlayView(int nPlayHandle, void* pParam, LP_WND_OBJ hWnd);

    /**
     * @brief 添加窗口
     * @param nPlayHandle 播放句柄
     * @param hWnd 窗口句柄
     * @param szPlayViewJsonParam 播放窗口信息，传空默认显示，详见下面example
     * @example
     * {
     *    "enable" : true,         ///< 【可选】渲染使能 true:渲染,false:不渲染【默认值:true】
     *    "channel_index": 0,      ///< 【可选】通道索引，表示第几个视频通道, 从0开始, 默认值：0
     *    "coord_vertices":        ///< 【可选】纹理坐标顶点 范围[0, 1]
     *    {
     *        "top": 1,【默认值:1】
     *        "bottom": 0,【默认值:0】
     *        "left": 0【默认值:0】
     *        "right": 1,【默认值:1】
     *    }
     * }
     * @return >=0成功，<0失败
     */
    XSDK_API int CALLBACK XCloudSDK_Play_AddPlayView(int nPlayHandle, LP_WND_OBJ hWnd, const char* szPlayViewJsonParam);

    /**
     * @brief 移除窗口
     * @param nPlayHandle 播放句柄
     * @return >=0成功，否则失败
     */
    XSDK_API int CALLBACK XCloudSDK_Play_RemovePlayView(int nPlayHandle, LP_WND_OBJ hWnd);

    /**
     * @brief 设置窗口属性
     * @param nPlayHandle 播放句柄
     * @param hWnd 窗口句柄
     * @param szPlayViewJsonParam 添加画面Json参数
     * @example
     * {
     *    "enable" : true,         ///< 【可选】渲染使能 true:渲染,false:不渲染【默认值:true】
     *    "channel_index": 0,      ///< 【可选】通道索引，表示第几个视频通道, 从0开始, 默认值：0
     *    "coord_vertices":        ///< 【可选】纹理坐标顶点 范围[0, 1]
     *    {
     *        "top": 1,【默认值:1】
     *        "bottom": 0,【默认值:0】
     *        "left": 0【默认值:0】
     *        "right": 1,【默认值:1】
     *    }
     * }
     * @return >=0成功，否则失败
     */
    XSDK_API int CALLBACK XCloudSDK_Play_SetPlayViewAttr(int nPlayHandle,
                                                         LP_WND_OBJ hWnd,
                                                         const char* szPlayViewJsonParam);

    /**
     * @brief 设置播放速度
     * @param nPlayHandle 播放句柄
     * @param nSpeed 播放速度
     * @return >=0成功，否则失败
     */
    XSDK_API int CALLBACK XCloudSDK_Play_SetPlaySpeed(int nPlayHandle, int nSpeed);

    /*
     * @brief 时间轴跳转(只支持文件播放接口XCloudSDK_Play_OpenFilePlay)
     * @param nPlayHandle 播放句柄
     * @param nPos 跳转到的时间轴百分比(0-100)
     * @return >=0成功，否则失败
     */
    XSDK_API int CALLBACK XCloudSDK_Play_SetPlayPosition(int nPlayHandle, int nPos);

#ifdef __cplusplus
}
#endif
#endif // XCPLAY_H
