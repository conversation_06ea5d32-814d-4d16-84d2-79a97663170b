﻿#pragma once
#include "XNetSDK/Json_Header/JObject.h"
#include "XNetSDK/Json_Header/CarInfo.h"
NS_NETSDK_CFG_BEGIN

#define JK_PgsTargetInfoAll "PgsTargetInfoAll"
class PgsTargetInfoAll: public JObject
{
public:
    JObjArray<CCarInfo> CarInfos;
    JIntObj Channel;

public:
    PgsTargetInfoAll(JObject* pParent = NULL, const char* szName = JK_PgsTargetInfoAll)
        : JObject(pParent, szName)
        , Channel(this, "Channel")
        , CarInfos(this, "CarInfo"){};

    ~PgsTargetInfoAll(void){};
};

NS_NETSDK_CFG_END