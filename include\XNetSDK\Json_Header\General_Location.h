﻿#pragma once
#include "XNetSDK/Json_Header/JObject_NetSDK.h"
#include "XNetSDK/Json_Header/DSTEnd.h"
#include "XNetSDK/Json_Header/DSTStart.h"
NS_NETSDK_CFG_BEGIN

#define JK_General_Location "General.Location"
class General_Location: public JObject
{
public:
    DSTEnd mDSTEnd;        // 夏令时结束
    JStrObj DSTRule;       // 夏令时规则
    DSTStart mDSTStart;    // 夏令时开始
    JStrObj DateFormat;    // 日期格式:“YYMMDD”, “MMDDYY”, “DDMMYY”* /
    JStrObj DateSeparator; // 日期分割符:“.”, “ - ”, “ / ” * /
    JStrObj Language; // 语言选择:“English”, “SimpChinese”, “TradChinese”, “Italian”, “Spanish”, “Japanese”, “Russian”,
                      // “French”, “German” */
    JStrObj TimeFormat;  // 时间格式:“12”, “24” */
    JStrObj VideoFormat; // 视频制式:“PAL”:0, “NTSC”:1, “SECAM” */
    JIntObj WorkDay;     // 工作日

public:
    General_Location(JObject* pParent = NULL, const char* szName = JK_General_Location)
        : JObject(pParent, szName)
        , mDSTEnd(this, "DSTEnd")
        , DSTRule(this, "DSTRule")
        , mDSTStart(this, "DSTStart")
        , DateFormat(this, "DateFormat")
        , DateSeparator(this, "DateSeparator")
        , Language(this, "Language")
        , TimeFormat(this, "TimeFormat")
        , VideoFormat(this, "VideoFormat")
        , WorkDay(this, "WorkDay"){};

    ~General_Location(void){};
};

NS_NETSDK_CFG_END