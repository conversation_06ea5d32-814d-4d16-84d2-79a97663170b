<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
    <PropertyGroup>
        <ZXingCpp-Platform Condition="'$(Platform)' == 'Win32'">x86</ZXingCpp-Platform>
        <ZXingCpp-Platform Condition="'$(Platform)' != 'Win32'">$(Platform)</ZXingCpp-Platform>
    </PropertyGroup>
    <ItemGroup Condition="'$(TargetPlatformIdentifier)' == 'UAP'">
        <Reference Include="$(MSBuildThisFileDirectory)..\..\lib\uap10.0\ZXing.winmd">
            <Implementation>ZXing.dll</Implementation>
        </Reference>
        <ReferenceCopyLocalPaths Include="$(MSBuildThisFileDirectory)..\..\runtimes\win10-$(ZXingCpp-Platform)\native\ZXing.dll" />
    </ItemGroup>
</Project>
