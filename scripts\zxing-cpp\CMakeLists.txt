cmake_minimum_required(VERSION 3.15)

project(ZXing)

option (BUILD_WRITERS "Build with writer support (encoders)" ON)
option (BUILD_READERS "Build with reader support (decoders)" ON)
option (BUILD_EXAMPLES "Build the example barcode reader/writer applications" ON)
option (BUILD_BLACKBOX_TESTS "Build the black box reader/writer tests" OFF)
option (BUILD_UNIT_TESTS "Build the unit tests (don't enable for production builds)" OFF)
option (BUILD_PYTHON_MODULE "Build the python module" OFF)
option (BUILD_C_API "Build the C-API" OFF)
option (BUILD_EXPERIMENTAL_API "Build with experimental API" OFF)
set(BUILD_DEPENDENCIES "AUTO" CACHE STRING "Fetch from github or use locally installed (AUTO/GITHUB/LOCAL)")

if (WIN32)
    option (BUILD_SHARED_LIBS "Build and link as shared library" OFF)
else()
    option (BUILD_SHARED_LIBS "Build and link as shared library" ON)
endif()

if (MSVC)
    add_definitions (-DUNICODE -D_UNICODE -D_CRT_SECURE_NO_WARNINGS)
    option (LINK_CPP_STATICALLY "MSVC only, link standard library statically (/MT and /MTd)" OFF)
    if (LINK_CPP_STATICALLY)
        set(CMAKE_MSVC_RUNTIME_LIBRARY "MultiThreaded$<$<CONFIG:Debug>:Debug>")
    endif()
endif()

if (NOT CMAKE_BUILD_TYPE)
    set (DEFAULT_BUILD_TYPE "Release")
    message (STATUS "Setting build type to '${DEFAULT_BUILD_TYPE}' as none was specified.")
    set (CMAKE_BUILD_TYPE "${DEFAULT_BUILD_TYPE}" CACHE STRING "Choose the type of build." FORCE)
    set_property (CACHE CMAKE_BUILD_TYPE PROPERTY STRINGS "Debug" "Release" "MinSizeRel" "RelWithDebInfo")
endif()

if (BUILD_SHARED_LIBS)
    set (CMAKE_WINDOWS_EXPORT_ALL_SYMBOLS ON)
endif()

if (NOT CMAKE_CXX_STANDARD)
    set (CMAKE_CXX_STANDARD 17)
endif()
if (NOT CMAKE_CXX_EXTENSIONS)
    set (CMAKE_CXX_EXTENSIONS OFF)
endif()

if (NOT (BUILD_READERS OR BUILD_WRITERS))
    message(FATAL_ERROR "At least one of BUILD_READERS/BUILD_WRITERS must be enabled.")
endif()

if (BUILD_UNIT_TESTS AND (NOT BUILD_WRITERS OR NOT BUILD_READERS))
    message("Note: To build with unit tests, the library will be build with READERS and WRITERS.")
    set (BUILD_WRITERS ON)
    set (BUILD_READERS ON)
endif()

if (BUILD_EXPERIMENTAL_API)
    add_definitions (-DZXING_BUILD_EXPERIMENTAL_API)
endif()

set(BUILD_DEPENDENCIES_LIST AUTO GITHUB LOCAL)
set_property(CACHE BUILD_DEPENDENCIES PROPERTY STRINGS ${BUILD_DEPENDENCIES_LIST})
if(NOT BUILD_DEPENDENCIES IN_LIST BUILD_DEPENDENCIES_LIST)
    message(FATAL_ERROR "BUILD_DEPENDENCIES must be one of ${BUILD_DEPENDENCIES_LIST}")
endif()

add_subdirectory (core)

enable_testing()

include(zxing.cmake)

if (BUILD_EXAMPLES)
    add_subdirectory (example)
endif()
if (BUILD_BLACKBOX_TESTS)
    add_subdirectory (test/blackbox)
endif()
if (BUILD_UNIT_TESTS)
    add_subdirectory (test/unit)
endif()
if (BUILD_PYTHON_MODULE)
    add_subdirectory (wrappers/python)
endif()
if (BUILD_C_API)
    add_subdirectory (wrappers/c)
endif()
