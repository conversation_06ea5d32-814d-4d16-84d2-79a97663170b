#!/usr/bin/env python3
"""
项目构建脚本
自动下载OpenCV依赖并构建项目
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

PROJECT_ROOT = Path(__file__).parent.parent
BUILD_DIR = PROJECT_ROOT / "build"
OPENCV_SCRIPT = PROJECT_ROOT / "scripts" / "download_opencv.py"

def run_command(cmd, cwd=None, check=True):
    """运行命令并显示输出"""
    print(f"运行命令: {' '.join(cmd)}")
    try:
        result = subprocess.run(cmd, cwd=cwd, check=check, text=True, 
                              capture_output=False)
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败: {e}")
        return False

def download_opencv():
    """下载OpenCV依赖"""
    print("=" * 50)
    print("步骤 1: 下载OpenCV依赖")
    print("=" * 50)
    
    if not OPENCV_SCRIPT.exists():
        print(f"错误: 下载脚本不存在: {OPENCV_SCRIPT}")
        return False
    
    return run_command([sys.executable, str(OPENCV_SCRIPT)])

def configure_cmake():
    """配置CMake"""
    print("=" * 50)
    print("步骤 2: 配置CMake")
    print("=" * 50)
    
    # 创建构建目录
    BUILD_DIR.mkdir(exist_ok=True)
    
    # CMake配置命令
    cmake_cmd = [
        "cmake",
        str(PROJECT_ROOT),
        "-DCMAKE_BUILD_TYPE=Release"
    ]
    
    # 平台特定配置
    if platform.system() == "Windows":
        cmake_cmd.extend([
            "-G", "Visual Studio 16 2019",
            "-A", "x64"
        ])
    
    return run_command(cmake_cmd, cwd=BUILD_DIR)

def build_project():
    """构建项目"""
    print("=" * 50)
    print("步骤 3: 构建项目")
    print("=" * 50)
    
    build_cmd = [
        "cmake",
        "--build", ".",
        "--config", "Release"
    ]
    
    # 添加并行构建
    if platform.system() != "Windows":
        build_cmd.extend(["-j", "4"])
    
    return run_command(build_cmd, cwd=BUILD_DIR)

def main():
    """主函数"""
    print("IDCamera项目构建脚本")
    print("=" * 50)
    print(f"项目根目录: {PROJECT_ROOT}")
    print(f"构建目录: {BUILD_DIR}")
    print(f"平台: {platform.system()} {platform.machine()}")
    print()
    
    try:
        # 步骤1: 下载OpenCV
        if not download_opencv():
            print("OpenCV下载失败")
            return 1
        
        # 步骤2: 配置CMake
        if not configure_cmake():
            print("CMake配置失败")
            return 1
        
        # 步骤3: 构建项目
        if not build_project():
            print("项目构建失败")
            return 1
        
        print("=" * 50)
        print("构建成功!")
        print("=" * 50)
        
        # 显示输出文件位置
        if platform.system() == "Windows":
            exe_path = BUILD_DIR / "Release" / "idcamera.exe"
        else:
            exe_path = BUILD_DIR / "idcamera"
        
        if exe_path.exists():
            print(f"可执行文件位置: {exe_path}")
        
        return 0
        
    except KeyboardInterrupt:
        print("\n构建被用户中断")
        return 1
    except Exception as e:
        print(f"构建过程中发生错误: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
