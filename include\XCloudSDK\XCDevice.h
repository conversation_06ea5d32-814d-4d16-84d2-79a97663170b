﻿/*********************************************************************************
 *Description:
 *History:
 Date:    2024.03.28
 Action：Create
 **********************************************************************************/
#pragma once
#ifndef XCDEVICE_H
#define XCDEVICE_H
#include "XBasic/XSDKPublic.h"

#ifdef __cplusplus
extern "C"
{
#endif

    /**
     * @brief 将设备信息缓存到本地
     * @param szDevInfo 设备信息Json格式(与添加设备格式相同)
     * @return >=0:成功; <0:失败;
     */
    XSDK_API int CALLBACK XCloudSDK_Device_AddDevInfoToDC(const char* szDevInfo);

    /**
     * @brief APP应用层获取设备状态
     * @param hUser 用户句柄
     * @param szDevIds 设备SN,多个设备间使用";"分隔
     * @param nSeq 自定义值
     * @return >=0:成功; <0:失败;
     * @retval id: 5009 = EACT_MSG_GET_DEV_STATE, 获取设备状态
     * @retval param1: 在线状态对应值 >0 为在线
     * @retval str: 设备序列号
     */
    XSDK_API int CALLBACK XCloudSDK_Device_SysGetDevState(int hUser, const char* szDevIds, int nSeq = 0);

    /**
     * @brief [同步]获取设备在线状态
     * @param szDevId 设备序列号
     * @param nStateType 对应 EXAccount_DevStatus 枚举
     * @return 对应 EFUN_SATE 枚举
     */
    XSDK_API int CALLBACK XCloudSDK_Device_GetDevState(const char* szDevId, int nStateType);

    /**
     * @brief 本地缓存设备用户名密码
     * @param szDevId 设备序列号或IP+Port
     * @param szUser 设备用户名
     * @param szPwd 设备密码
     * @return ==0 成功
     */
    XSDK_API int CALLBACK XCloudSDK_Device_SetLocalUserNameAndPwd(const char* szDevId,
                                                                  const char* szUser,
                                                                  const char* szPwd);

    /**
     * @brief 本地缓存设备Token
     * @param szDevId 设备序列号或IP+Port
     * @param szToken 设备Token
     * @return ==0 成功
     */
    XSDK_API int CALLBACK XCloudSDK_Device_SetLocalToken(const char* szDevId, const char* szToken);

    /**
     * @brief 从本地缓存获取设备用户名
     * @param szDevId 设备序列号或IP+Port
     * @param szUserName 设备用户名
     * @return 设备用户名
     */
    XSDK_API char* CALLBACK XCloudSDK_Device_GetLocalUserName(const char* szDevId, char szUserName[64]);

    /**
     * @brief 从本地缓存获取设备密码
     * @param szDevId 设备序列号或IP+Port
     * @param szPwd 设备密码
     * @return 设备密码
     */
    XSDK_API char* CALLBACK XCloudSDK_Device_GetLocalPwd(const char* szDevId, char szPwd[80]);

    /**
     * @brief 从本地缓存获取设备token
     * @param pDeviceSN 设备序列号
     * @param sOutResult [Out]结果值
     * @return 结果值
     */
    XSDK_API char* XCloudSDK_Device_GetLocalToken(const char* szDevId, char sOutResult[64]);

    /**
     * @brief 同步设备信息到本地
     * @param sInfo 设备列表信息
     * @return
     */
    XSDK_API int XCloudSDK_Device_SynDevsInfo(const char* sInfo);

    /**
     * @brief 从SDK本地缓存中清除设备信息
     * @param szDevIds 设备序列号，支持批量，用';'隔开
     * @return 无
     */
    XSDK_API void XCloudSDK_Device_DeleteDevsInfo(const char* szDevIds);

    /**
     * @brief 登录设备
     * @param hUser 用户句柄
     * @param szDevId 设备序列号或IP+Port
     * @param nSeq 自定义值
     * @return 异步接口，通过回调判断是否成功
     * @retval id: 12001 = ESXSDK_DEV_LOGIN, 登录结果返回
     * @retval param1: >=0成功，否则失败
     */
    XSDK_API int CALLBACK XCloudSDK_Device_DevLogin(int hUser, const char* szDevId, int nSeq = 0);

    /**
     * @brief 登出设备
     * @param szDevId 设备序列号
     * @return ==0 成功
     */
    XSDK_API int CALLBACK XCloudSDK_Device_DevLogout(const char* szDevId);

    /**
     * @brief 实时预览
     * @param hUser 用户句柄
     * @param szDevId 设备序列号或IP+Port
     * @param nChannel 设备通道号
     * @param nStreamType 码流类型
     * @param hWnd 窗口句柄
     * @param nSeq 自定义值
     * @param szJsonParam 拓展参数，Json格式，格式参考接口文档
     * @return >0:成功，对应播放句柄;<=0:失败
     * @retval id: 12002 = ESXSDK_MEDIA_START_REAL_PLAY, 实时视频结果返回
     * @retval param1: >=0成功，否则失败
     */
    XSDK_API int CALLBACK XCloudSDK_Device_MediaRealPlay(int hUser,
                                                         const char* szDevId,
                                                         int nChannel,
                                                         int nStreamType,
                                                         LP_WND_OBJ hWnd,
                                                         int nSeq = 0,
                                                         const char* szJsonParam = "");

    /**
     * @brief 设备端对讲
     * @param hUser 用户句柄
     * @param szDevId 设备序列号或IP+Port
     * @param szParam 对讲请求参数，JSON格式
     * @param nSeq 自定义值
     * @return >0:对讲句柄;<=0:失败
     * @retval id: 12007 = ESXSDK_MEDIA_START_TALK, 开始对讲回调
     * @retval param1: >=0成功，否则失败
     */
    XSDK_API int CALLBACK XCloudSDK_Device_StartTalk(int hUser, const char* szDevId, const char* szParam, int nSeq = 0);

    /**
     * @brief 发送对讲数据
     * @param nPlayHandle 对讲句柄
     * @param pPCMData PCM格式的音频数据
     * @param nDataLen PCM数据长度
     * @return  >=0:成功 <0:失败
     */
    XSDK_API int CALLBACK XCloudSDK_Device_SendTalkData(int nPlayHandle, const char* pPCMData, int nDataLen);

    /**
     * @brief 开启音视频对讲
     * @param hUser 接收用户对象
     * @param szDevId 设备序列号
     * @param szJsonParam 请求信息
     * @param nSeq 用户自定义值
     * @param nTimeout 超时时间
     * @return >0:音视频对讲句柄;<=0:失败
     * @example szJsonParam:
     *  {
     *      "version": 1,   // 私有码流版本 0: v1, 1: v2
     *      "channel": 0,   // 通道号
     *      "video": {
     *          "h265_tag": 0,              // h265是否需要特殊编码参数, PID=A908007E5000000X的产品需要
     *          "rotate": 90,               // 选填, 顺时针旋转, 支持90,-90,180
     *          "from": {
     *              "format": "yuv",        // 编码类型, yuv/nv21/h264/h265/jpeg
     *              "width": 1920,          // 宽
     *              "height": 1080,         // 高
     *              "fps": 30               // 帧率
     *          },
     *          "to": {
     *              "format": "h264",
     *              "width": 800,
     *              "height": 600,
     *              "fps": 10
     *          }
     *      },
     *      "audio": {
     *          "from": {
     *              "format": "pcm",       // g711a/g711u/pcm
     *              "sample_rate": 44100,  // 采样率
     *              "bits_per_sample": 16, // 每个采样点占多少位
     *              "channels": 2          // 声道数
     *          },
     *          "to": {
     *              "format": "g711a",      // g711a/g711u/pcm
     *              "sample_rate": 8000,    // 采样率
     *              "bits_per_sample": 16,  // 每个采样点占多少位
     *              "channels": 1           // 声道数
     *          }
     *      }
     *  }
     */
    XSDK_API int CALLBACK
    XCloudSDK_Device_StartAvTalk(int hUser, const char* szDevId, const char* szParam, int nTimeout, int nSeq);

    /**
     * @brief 发送音视频对讲数据
     * @param hAvTalkHanlde 音视频对讲句柄
     * @param szAvData 音视频数据(完整一帧数据)
     * @param nDataLen 数据长度
     * @param nType 媒体类型, 0: 视频, 1: 音频
     * @return >=0:成功;<0:失败
     */
    XSDK_API int CALLBACK XCloudSDK_Device_SendAvTalkData(int hAvTalkHanlde,
                                                          const char* szAvData,
                                                          int nDataLen,
                                                          int nType);

    /**
     * @brief 使能: 设备端是否解码, 默认解码, 如需设备端停止解码, 恢复logo, 可调用此接口
     *
     * @param hAvTalkHanlde  对讲句柄
     * @param enable         使能
     * @return int
     */
    XSDK_API int CALLBACK XCloudSDK_Device_EnableDeviceVideoDecode(int hAvTalkHanlde, bool enable);

    /**
     * @brief 更新视频对讲参数
     *
     * @param hAvTalkHanlde 对接句柄
     * @param param  以下参数都是选填
     * {
     *     "video": {
     *        "format": "yuv",            // 编码类型, yuv/nv21/h264/h265/jpeg
     *        "width": 1920,              // 宽
     *        "height": 1080,             // 高
     *        "rotate": 90,               // 顺时针旋转, 支持0, 90,-90,180
     *     }
     * }
     * @return int
     */
    XSDK_API int CALLBACK XCloudSDK_Device_UpdateAvTalkParam(int hAvTalkHanlde, const char* param);

    /**
     * @brief 设置回声消除
     * @param nTalkHandle 对讲句柄
     * @param nPlayHandle 需要回声消除功能时，赋值参考的消除对象句柄
     * @param szParam 音频效果参数
     * @return >=0成功，<0失败
     * @example szParam:
     *  {
     *      "AudioEffects": {
     *          "AEC": {
     *              "Enable": true
     *          }
     *      }
     *  }
     */
    XSDK_API int CALLBACK XCloudSDK_Device_EnableAEC(int nTalkHandle, int nPlayHandle, const char* szParam);

    /**
     * @brief 关闭媒体播放
     * @param nPlayHandle 预览/回放/下载/对讲句柄
     * @return
     */
    XSDK_API void CALLBACK XCloudSDK_Device_StopMediaPlay(int nPlayHandle);

    /**
     * @brief 云台控制
     * @param hUser 用户句柄
     * @param szDevId 设备序列号或IP+Port
     * @param nChnIndex 通道号
     * @param szPTZCommand 云台控制命令
     * @param bStop 开始运动/停止运动
     * @param nSpeed 移动速度
     * @param nSeq 自定义值
     * @return  >=0:成功 <0:失败
     */
    XSDK_API int CALLBACK XCloudSDK_Device_PTZControl(int hUser,
                                                      const char* szDevId,
                                                      int nChnIndex,
                                                      const char* szPTZCommand,
                                                      bool bStop,
                                                      int nSpeed,
                                                      int nSeq = 0);

    /**
     * @brief 预置点操作
     * @param hUser 用户句柄
     * @param szDevId 设备序列号或IP+Port
     * @param nChannel 通道号
     * @param szPTZCommand 云台操作命令
     * @param nPreset 预置点
     * @return  >=0:成功 <0:失败
     */
    XSDK_API int CALLBACK
    XCloudSDK_Device_DevPtzPreset(int hUser, const char* szDevId, int nChannel, const char* szPTZCommand, int nPreset);

    /**
     * @brief 设置巡航点
     * @param hUser 用户句柄
     * @param szDevId 设备序列号或IP+Port
     * @param nChannel 通道号
     * @param nPresetPoint 预置点编号
     * @param nStep 步长
     * @param nTourIndex 巡航线路编号
     * @param bAdd 是否添加
     * @return  >=0:成功 <0:失败
     */
    XSDK_API int CALLBACK XCloudSDK_Device_SetPtzTourPoint(int hUser,
                                                           const char* szDevId,
                                                           int nChannel,
                                                           int nPresetPoint,
                                                           int nStep,
                                                           int nTourIndex,
                                                           bool bAdd);

    /**
     * @brief 删除巡航点
     * @param hUser 用户句柄
     * @param szDevId 设备序列号或IP+Port
     * @param nChannel 通道号
     * @param nTourIndex 巡航线路编号
     * @return  >=0:成功 <0:失败
     */
    XSDK_API int CALLBACK XCloudSDK_Device_DeleteDevPtzTour(int hUser,
                                                            const char* szDevId,
                                                            int nChannel,
                                                            int nTourIndex);

    /**
     * @brief 开始巡航
     * @param hUser 用户句柄
     * @param szDevId 设备序列号或IP+Port
     * @param nChannel 通道号
     * @param nTourIndex 巡航索引
     * @param bStart 是否开始
     * @param nTourTimes 巡航次数
     * @return  >=0:成功 <0:失败
     */
    XSDK_API int CALLBACK XCloudSDK_Device_DevPtzTour(int hUser,
                                                      const char* szDevId,
                                                      int nChannel,
                                                      int nTourIndex,
                                                      bool bStart,
                                                      int nTourTimes = 1);

    /**
     * @brief 获取设备系统配置
     * @param hUser 用户句柄
     * @param szDevId 设备序列号或IP+Port
     * @param szCommandName 配置命令字
     * @param nCommand 消息值
     * @param nTimeout 超时时间
     * @param nSeq 自定义值
     * @return
     * @retval id: 12101 = ESXSDK_DEV_GET_SYS_CONFIG, 设备系统配置获取
     * @retval param1: >=0成功，否则失败
     */
    XSDK_API int CALLBACK XCloudSDK_Device_DevGetSysConfig(int hUser,
                                                           const char* szDevId,
                                                           const char* szCommandName,
                                                           int nCommand = 1042,
                                                           int nTimeout = 5000,
                                                           int nSeq = 0);

    /**
     * @brief 设置设备系统配置
     * @param hUser 用户句柄
     * @param szDevId 设备序列号或IP+Port
     * @param szCommandName 配置命令字
     * @param pConfig 要设置的内容
     * @param nCommand 消息值
     * @param nTimeout 超时时间
     * @param nSeq 自定义值
     * @return
     * @retval id: 12102 = ESXSDK_DEV_SET_SYS_CONFIG, 设备系统配置设置
     * @retval param1: >=0成功，否则失败
     */
    XSDK_API int CALLBACK XCloudSDK_Device_DevSetSysConfig(int hUser,
                                                           const char* szDevId,
                                                           const char* szCommandName,
                                                           const char* pConfig,
                                                           int nCommand = 1040,
                                                           int nTimeout = 5000,
                                                           int nSeq = 0);

    /**
     * @brief 获取设备通道配置
     * @param hUser 用户句柄
     * @param szDevId 设备序列号或IP+Port
     * @param nChannel 通道号
     * @param szCommandName 配置命令字
     * @param nCommand 消息值
     * @param nTimeout 超时时间
     * @param nSeq 自定义值
     * @return
     * @retval id: 12103 = ESXSDK_DEV_GET_CHN_CONFIG，设备通道配置获取
     * @retval param1：>=0成功，否则失败
     * @retval param2：返回Json的长度
     * @retval param3：一般返回传入的nCommand+1
     * @retval str：对应传入的配置命令字szCommandName
     * @retval pData：Json数据
     */
    XSDK_API int CALLBACK XCloudSDK_Device_DevGetChnConfig(int hUser,
                                                           const char* szDevId,
                                                           int nChannel,
                                                           const char* szCommandName,
                                                           int nCommand = 1042,
                                                           int nTimeout = 5000,
                                                           int nSeq = 0);

    /**
     * @brief 设置设备通道配置
     * @param hUser 用户句柄
     * @param szDevId 设备序列号或IP+Port
     * @param nChannel 通道号
     * @param szCommandName 配置命令字
     * @param pConfig 设置的Json数据
     * @param nCommand 消息值
     * @param nTimeout 超时时间
     * @param nSeq 自定义值
     * @return
     * @retval id: 12104 = ESXSDK_DEV_SET_CHN_CONFIG, 设备通道配置设置
     * @retval param1：>=0成功，否则失败
     * @retval param2：返回Json的长度
     * @retval param3：一般返回传入的nCommand+1
     * @retval str：对应传入的配置命令字szCommandName
     * @retval pData：Json数据
     */
    XSDK_API int CALLBACK XCloudSDK_Device_DevSetChnConfig(int hUser,
                                                           const char* szDevId,
                                                           int nChannel,
                                                           const char* szCommandName,
                                                           const char* pConfig,
                                                           int nCommand = 1040,
                                                           int nTimeout = 5000,
                                                           int nSeq = 0);

    /**
     * @brief 设备系统配置（不登录）
     * @param hUser 用户句柄
     * @param szDevId 设备序列号/ip+port
     * @param szCommandName 配置命令字
     * @param szConfig 设置的Json数据
     * @param nCmdReq 消息值
     * @param nTimeout 超时时间
     * @param nSeq 自定义值
     * @return
     * @retval id: 等于传入的消息值nCmdReq+1
     * @retval param1: >=0成功，否则失败
     */
    XSDK_API int CALLBACK XCloudSDK_Device_DevSysConfig_NotLogin(int hUser,
                                                                 const char* szDevId,
                                                                 const char* szCommandName,
                                                                 const char* szConfig,
                                                                 int nCmdReq,
                                                                 int nTimeout = 5000,
                                                                 int nSeq = 0);

    /**
     * @brief 设备通道配置（不登录）
     * @param hUser 用户句柄
     * @param szDevId 设备序列号/ip+port
     * @param nChannel 通道号
     * @param szCommandName 配置命令字
     * @param szConfig 设置的Json数据
     * @param nCmdReq 消息值
     * @param nTimeout 超时时间
     * @param nSeq 自定义值
     * @return
     * @retval id: 等于传入的消息值nCmdReq+1
     * @retval param1: >=0成功，否则失败
     */
    XSDK_API int CALLBACK XCloudSDK_Device_DevChnConfig_NotLogin(int hUser,
                                                                 const char* szDevId,
                                                                 int nChannel,
                                                                 const char* szCommandName,
                                                                 const char* szConfig,
                                                                 int nCmdReq,
                                                                 int nTimeout = 5000,
                                                                 int nSeq = 0);

    /**
     * @brief 修改设备密码
     * @param hUser 用户句柄
     * @param szDevId 设备序列号
     * @param szUserName 设备登录用户名
     * @param szOldPwd 旧密码
     * @param szNewPwd 新密码
     * @param nTimeout 超时时间
     * @param nSeq 自定义值
     * @return >=0成功，否则失败
     */
    XSDK_API int CALLBACK XCloudSDK_Device_ModifyDevicePassword(int hUser,
                                                                const char* szDevId,
                                                                const char* szUserName,
                                                                const char* szOldPwd,
                                                                const char* szNewPwd,
                                                                int nTimeout = 5000,
                                                                int nSeq = 0);

    /**
     * @brief 局域网内搜索设备
     * @param hUser 用户句柄
     * @param nSearchTime 搜索时间: 2000~10000 单位ms
     * @param nSeq 自定义值
     * @return
     * @retval id: 13001 = ESXSDK_ON_SEARCH_DEVICES, 局域网下搜索设备
     * @retval param1: >=0成功，否则失败
     * @retval param2：搜索到的设备数量
     * @retval pObject：返回搜索到的设备信息，JSON格式
     */
    XSDK_API int CALLBACK XCloudSDK_Device_SearchDevices(int hUser, int nSearchTime = 10000, int nSeq = 0);

    /**
     * @brief 局域网内搜索指定设备
     * @param hUser 用户句柄
     * @param nSearchTime 搜索时间: 2000~10000 单位ms
     * @param szDevId 指定搜索某台设备的序列号，为空时与通用搜索接口一致
     * @param nSeq 自定义值
     * @return 通过回调函数获取结果
     * @retval id: 13001 = ESXSDK_ON_SEARCH_DEVICES, 局域网下搜索设备
     * @retval param1: >=0成功，否则失败
     * @retval param2：搜索到的设备数量
     * @retval pObject：返回搜索到的设备信息，JSON格式
     */
    XSDK_API int CALLBACK XCloudSDK_Device_SearchSpecifiedDev(int hUser,
                                                              int nSearchTime = 10000,
                                                              const char* szDevId = "",
                                                              int nSeq = 0);

    /**
     * @brief 开始发送文件信息到设备（目前主要用于发送自定义报警语音文件）
     * @param hUser 用户句柄
     * @param szDevId 设备序列号
     * @param szJson 请求的Json内容
     * @param szFileName 文件名
     * @param nTimeout 超时时间
     * @param nSeq 自定义值
     * @return 文件句柄
     * @retval id: 13010 = ESXSDK_DEV_SENDFILE, 发送文件状态返回
     * @retval param1: 参考ESendFileStep枚举：0-发送中  1-出现异常，发送结束  2-发送完成
     * @retval param2：发送进度
     * @retval str：设备返回的消息
     */
    XSDK_API int CALLBACK XCloudSDK_Device_StartSendFileToDev(int hUser,
                                                              const char* szDevId,
                                                              const char* szJson,
                                                              const char* szFileName,
                                                              int nTimeout = 5000,
                                                              int nSeq = 0);

    /**
     * @brief 关闭发送文件
     * @param nSendFile 文件句柄---XCloudSDK_Device_StartSendFileToDev接口返回值
     * @return
     */
    XSDK_API void CALLBACK XCloudSDK_Device_StopSendFileToDev(int nSendFile);

    /**
     * @brief 查询设备端录像日历
     * @param hUser 用户句柄
     * @param szDevId 设备序列号或IP+Port
     * @param szParam 查询请求Json参数
     * @param nTimeout 超时时间
     * @param nSeq 自定义值
     * @return >=0:成功;<0:失败
     * @retval id: 12110 = ESXSDK_DEV_ON_SEARCH_CALENDAR, 查询录像日历
     * @retval param1: >=0成功，否则失败
     * @retval pObject: 结果返回，SXSDKSearchCalendarRes
     */
    XSDK_API int CALLBACK XCloudSDK_Device_SearchCalendar(int hUser,
                                                          const char* szDevId,
                                                          const char* szParam,
                                                          int nTimeout = 5000,
                                                          int nSeq = 0);

    /**
     * @brief 查询设备端录像文件
     * @param hUser 用户句柄
     * @param szDevId 设备序列号或IP+Port
     * @param szParam 查询文件请求Json参数
     * @param nSeq 自定义值
     * @return >=0:成功;<0:失败
     * @retval id: 12105 = ESXSDK_DEV_FIND_FILE, 返回查询到录像文件信息
     * @retval param1: >=0成功，否则失败
     * @retval str：返回JSONName
     * @retval pObject：返回设备的JSON内容
     */
    XSDK_API int CALLBACK XCloudSDK_Device_FindRecordFile(int hUser,
                                                          const char* szDevId,
                                                          const char* szParam,
                                                          int nSeq = 0);

    /**
     * @brief 查询时间段录像信息
     * @param hUser 用户句柄
     * @param szDevId 设备序列号或IP+Port
     * @param szParam 查询文件请求Json参数
     * @param nSeq 自定义值
     * @return >=0:成功;<0:失败
     * @retval id: 12111 = ESXSDK_DEV_FIND_FILE_BYTIME, 返回录像信息结果
     * @retval pObject：返回查询结果结构体
     */
    XSDK_API int CALLBACK XCloudSDK_Device_FindRecordFileByTime(int hUser,
                                                                const char* szDevId,
                                                                const char* szParam,
                                                                int nSeq = 0);

    /**
     * @brief 设备端录像远程回放
     * @param hUser 用户句柄
     * @param szDevId 设备序列号或IP+Port
     * @param szParam 录像请求Json参数
     * @param hWnd 窗口句柄
     * @param nSeq 自定义值
     * @return >0:成功，对应播放句柄;<=0:失败
     * @retval id: 12012 = ESXSDK_MEDIA_START_RECORD_PLAY_BYNAME, 按文件名称开始回放播放
     * @retval param1: >=0成功，否则失败
     */
    XSDK_API int CALLBACK XCloudSDK_Device_MediaRecordPlay(int hUser,
                                                           const char* szDevId,
                                                           const char* szParam,
                                                           LP_WND_OBJ hWnd,
                                                           int nSeq = 0);

    /**
     * @brief 按时间设备端录像远程回放
     * @param hUser 用户句柄
     * @param szDevId 设备序列号或IP+Port
     * @param szParam 录像请求Json参数
     * @param hWnd 窗口句柄
     * @param nSeq 自定义值
     * @return >0:成功，对应播放句柄;<=0:失败
     * @retval id: 12004 = ESXSDK_MEDIA_START_RECORD_PLAY, 开始回放播放
     * @retval param1: >=0成功，否则失败
     */
    XSDK_API int CALLBACK XCloudSDK_Device_MediaRecordPlayByTime(int hUser,
                                                                 const char* szDevId,
                                                                 const char* szParam,
                                                                 LP_WND_OBJ hWnd,
                                                                 int nSeq = 0);

    /**
     * @brief 设备端录像下载(可以下载图片&视频)
     * @param hUser 用户句柄
     * @param szDevId 设备序列号或IP+Port
     * @param szParam 录像下载请求Json参数
     * @param nSeq 自定义值
     * @return >0:成功，对应播放句柄;<=0:失败
     * @retval id: 12006 = ESXSDK_MEDIA_DOWN_IMAGES_FILE, 录像或图片下载
     * @retval param1: >=0成功，否则失败
     */
    XSDK_API int CALLBACK XCloudSDK_Device_MediaRecordDownload(int hUser,
                                                               const char* szDevId,
                                                               const char* szParam,
                                                               int nSeq = 0);

    /**
     * @brief 按时间设备端录像下载
     * @param hUser 用户句柄
     * @param szDevId 设备序列号或IP+Port
     * @param szParam 录像下载请求Json参数
     * @param nSeq 自定义值
     * @return >0:成功，对应播放句柄;<=0:失败
     * @retval id: 12005 = ESXSDK_MEDIA_DOWN_RECORD_FILE, 录像下载
     * @retval param1: >=0成功，否则失败
     */
    XSDK_API int CALLBACK XCloudSDK_Device_MediaRecordDownloadByTime(int hUser,
                                                                     const char* szDevId,
                                                                     const char* szParam,
                                                                     int nSeq = 0);

    /**
     * @brief 录像回放/下载控制->暂停/继续
     * @param nPlayHandle 回放接口返回句柄
     * @param bPause 是否暂停
     * @return >=0:成功;<0:失败
     */
    XSDK_API int CALLBACK XCloudSDK_Device_MediaPause(int nPlayHandle, bool bPause);

    /**
     * @brief 时间跳转
     * @param nPlayHandle 回放接口返回句柄
     * @param szTime 时间点。格式：YYYY-MM-DD HH:mm:SS
     * @return >=0:成功;<0:失败
     */
    XSDK_API int CALLBACK XCloudSDK_Device_MediaSeekToTime(int nPlayHandle, const char* szTime);

    /**
     * @brief 获取本地设备缓存信息
     * @param hUser 用户句柄
     * @param szDevId 设备序列号或IP+Port
     * @param szType 获取数据类型。目前支持“SystemInfo”和“SystemFunction”
     * @return
     * @retval id: 12101 = ESXSDK_DEV_GET_SYS_CONFIG, 设备系统配置获取
     * @retval param1: >=0成功，否则失败
     */
    XSDK_API int CALLBACK XCloudSDK_Device_GetDeviceInfo(int hUser, const char* szDevId, const char* szType);

    /**
     * @brief 获取当前设备网络类型（同步接口）
     * @param szDevId 设备序列号或IP+Port
     * @return >=0:对应不同类型;<0:未登录状态。对应EDEV_CNN_TYPE
     */
    XSDK_API int CALLBACK XCloudSDK_Device_GetCurNetType(const char* szDevId);

    /**
     * @brief 获取当前设备类型（同步接口）
     * @param szDevId 设备序列号或IP+Port
     * @param sOutDevType [输出]设备类型
     * @return 设备类型
     */
    XSDK_API char* CALLBACK XCloudSDK_Device_GetDevType(const char* szDevId, char sOutDevType[64]);

    /**
     * @brief 同步设备系统时间
     * @param szDevId 设备序列号或IP+Port
     * @param szSysTime 时间格式：2018-07-25 17:20:37; == NULL或""时，默认为当前系统时间
     * @param nSeq 用户自定义值
     * @return
     * @retval id: 12102 = ESXSDK_DEV_SET_SYS_CONFIG, 修改设备系统配置
     * @retval param1: >=0成功，否则失败
     */
    XSDK_API int CALLBACK XCloudSDK_Device_DevSynTime(int hUser,
                                                      const char* szDevId,
                                                      const char* szSysTime = "",
                                                      int nSeq = 0);

    /**
     * @brief 下载图片的缩略图
     * @param hUser 用户句柄
     * @param szDevId 设备序列号或IP+Port
     * @param szFileName 文件名
     * @param szInJsonParam 请求的JSON信息
     * @param nMsgId 消息ID,默认EXCMD_COMPRESS_PICTURE_REQ=1448
     * @param nTimeout 超时时间
     * @param nSeq 自定义值
     * @return >=0:成功;<0:失败
     * @retval id: 12112 = ESXSDK_DEV_COMPRESS_PICTURE, 获取缩略图
     * @retval param1: 对应请求结果—小于0表示失败，
     * 等于0表示开始下载，等于1表示在当前下载路径已存在该图片（之前已下载过）
     * @retval param2: 对应图片数据长度
     * @retval param3: 对应消息id
     * @retval str: 对应图片名称
     * @retval pObject: 对应图片数据
     */
    XSDK_API int CALLBACK XCloudSDK_Device_AddDownloadComPicToTaskList(int hUser,
                                                                       const char* szDevId,
                                                                       const char* szFileName,
                                                                       const char* szInJsonParam,
                                                                       int nMsgId = 1448,
                                                                       int nTimeout = 5000,
                                                                       int nSeq = 0);

    /**
     * @brief 取消下载图片的缩略图
     * @param hUser 用户句柄
     * @param szDevId 设备序列号或IP+Port
     * @return
     */
    XSDK_API void CALLBACK XCloudSDK_Device_CancelDownloadCompressPicture(int hUser, const char* szDevId);

    /**
     * @brief 下载录像缩略图---支持批量下载
     * @param hUser 用户句柄
     * @param szDevId 设备序列号或IP+Port
     * @param szInJsonParam 请求的JSON信息
     * @param nSeq 自定义值
     * @return >0:返回下载句柄;<=0:失败
     * @retval 配合取消下载XSDK_CancelDownloadRecordThumbnail一起使用
     * @retval id: 12113 = ESXSDK_DEV_RECORD_PIC_START, 开始下载  nParam1:查询的图片数，小于0表示失败错误码
     * @retval id: 15002 = ESXSDK_DEV_RECORD_PIC, 下载中 nParam1:图片长度，nParam2:当前已获取图片数，nParam3:时间戳(秒),
     * szString:对应图片的名称，pObject:图片数据
     * @retval id: 15003 = ESXSDK_DEV_RECORD_PIC_COMPLETE, 下载缩略图结束
     */
    XSDK_API int CALLBACK XCloudSDK_Device_DownloadRecordThumbnail(int hUser,
                                                                   const char* szDevId,
                                                                   const char* szInJsonParam,
                                                                   int nSeq = 0);

    /**
     * @brief 取消下载录像缩略图
     * @param hUser 用户句柄
     * @param hDownloadHandle 下载句柄
     * @return
     */
    XSDK_API void CALLBACK XCloudSDK_Device_CancelDownloadRecordThumbnail(int hUser, int hDownloadHandle);

    /**
     * @brief 判断是否为已搜索到的设备
     * @param szDevId 设备序列号
     * @return true:找到；false:没找到
     */
    XSDK_API bool CALLBACK XCloudSDK_Device_DevIsSearched(const char* szDevId);

    /**
     * @brief 检测设备是否需要升级, 支持多模块检测 前提: 如果是拓展模块,
     * 需要调用`XSDK_SetObjStrAttr`设置`ESDK_OBJECT_ATTR_DEVICE_PID`属性
     * @param hUser 用户句柄
     * @param szDevId 设备序列号
     * @param szReqJson 请求参数
     * // 通用设备
     *    {
     *        "Type": "Common",         // 通用设备固定为`Common`
     *        "Language": "Chinese",    // 语言
     *        "SystemInfo": {...},      // 通过设备配置获取(SystemInfo)
     *        "EnableServerCheck" : true,   // 是否启用服务器检测(可选)---默认启用
     *        "Channel" : -1            // 通道号(可选);不填或-1:默认查询设备本身;0~n，查询NVR前端的IPC通道
     *    }
     * // 拓展设备
     *    {
     *        "Type": "MCU",            // 这里是拓展设备的具体类型
     *        "Language": "Chinese",    // 语言
     *        "UUID": "xxxxxxxxxxxxxx", // 从SystemInfo中获取
     *        "SystemInfoEx": {...},    // 通过设备配置获取(SystemInfoEx)
     *        "EnableServerCheck" : true   // 是否启用服务器检测(可选)---默认启用
     *    }
     * @param nTimeout 超时时间
     * @param nSeq 自定义值
     * @return
     * @retval id: 13400 = ESXSDK_DEV_UPGRADE_CHECK，升级检测回调消息
     * @detail 默认先走设备检测，失败走服务器检测。支持禁用服务器检测，只进行设备检测
     */
    XSDK_API int CALLBACK XCloudSDK_Device_DevUpgradeCheck(int hUser,
                                                           const char* szDevId,
                                                           const char* szReqJson,
                                                           int nTimeout = 5000,
                                                           int nSeq = 0);

    /**
     * @brief 直接向服务器检测版本信息, 只有蓝牙模块的设备可调用该接口检测版本信息
     * @param hUser 用户句柄
     * @param szReqJson 请求参数 与XCloudSDK_Device_DevUpgradeCheck接口相同
     * @param nTimeout 超时时间
     * @param nSeq 自定义值
     * @return
     * @retval id: 13400 = ESXSDK_DEV_UPGRADE_CHECK，升级检测回调消息
     */
    XSDK_API int CALLBACK XCloudSDK_Device_UpgradeCheckFromServer(int hUser,
                                                                  const char* szReqJson,
                                                                  int nTimeout = 5000,
                                                                  int nSeq = 0);

    /**
     * @brief 发送文件升级设备
     * @param hUser         用户句柄
     * @param szDevId       设备序列号
     * @param szFileName    文件名称
     * @param nTimeout      超时时间
     * @param nSeq          自定义值
     * @return
     * @retval id:13006 = ESXSDK_DEV_UPGRADE，设备升级返回
     * @retval param1: 对应EUpgradeStep枚举
     * @retval param2: 表示进度，0-100
     * @retval str: 设备返回的信息（暂不使用）
     */
    XSDK_API int CALLBACK XCloudSDK_Device_UpgradeByFile(int hUser,
                                                         const char* szDevId,
                                                         const char* szFileName,
                                                         int nTimeout = 5000,
                                                         int nSeq = 0);

    /**
     * @brief 发送升级文件到设备固定模块
     * @param hUser         用户句柄
     * @param szDevId       设备序列号
     * @param szReqJson     请求的JSON数组
     * @param nTimeout      超时时间
     * @param nSeq          自定义值
     * @return
     * @retval id:13006 = ESXSDK_DEV_UPGRADE，设备升级返回
     * @retval param1: 对应EUpgradeStep枚举
     * @retval param2: 表示进度，0-100
     * @retval str: 设备返回的信息（暂不使用）
     * JSON格式：
     * [{
     *  "FileName" : "",       // 固件名
     *  "ModuleType" : "Mcu"   // 需要升级的模块类型
     * }]
     */
    XSDK_API int CALLBACK XCloudSDK_Device_UpgradeByFileEx(int hUser,
                                                           const char* szDevId,
                                                           const char* szReqJson,
                                                           int nTimeout = 5000,
                                                           int nSeq = 0);

    /**
     * @brief 设备自主升级
     * @param hUser     用户句柄
     * @param szDevId   设备序列号
     * @param szReqJson 请求参数,来源: `XCloudSDK_Device_DevUpgradeCheck`回调返回的`str`
     * @param nTimeout  超时时间
     * @param nSeq      用户自定义值
     * @return
     * @retval id:13006 = ESXSDK_DEV_UPGRADE，设备升级返回
     * @retval param1: 对应EUpgradeStep枚举
     * @retval param2: 表示进度，0-100
     */
    XSDK_API int CALLBACK XCloudSDK_Device_DevUpgradeSelf(int hUser,
                                                          const char* szDevId,
                                                          const char* szReqJson,
                                                          int nTimeout = 5000,
                                                          int nSeq = 0);

    /**
     * @brief 下载设备固件
     * @param hUser     用户句柄
     * @param szReqJson 请求参数,来源:
     * XSDK_DevUpgradeCheck或XCloudSDK_Device_UpgradeCheckFromServer或XCloudSDK_Device_DevUpgradeSelf回调返回的str
     * @param dir       文件保存目录
     * @param nTimeout  超时时间
     * @param nSeq      用户自定义值
     * @return
     * @retval id:13401 = ESXSDK_DEV_UPGRADE_DOWNLOAD，下载设备固件
     * @retval param1: 返回值
     */
    XSDK_API int CALLBACK XCloudSDK_Device_DevUpgradeDownloadFile(int hUser,
                                                                  const char* szReqJson,
                                                                  const char* dir,
                                                                  int nTimeout = 5000,
                                                                  int nSeq = 0);

    /**
     * @brief 设备唤醒
     * @param hUser 用户句柄
     * @param szDevId 设备序列号
     * @param nTimeout 超时时间
     * @param nSeq 自定义值
     * @return
     * @retval id: 5214 = EACT_MSG_WAKE_UP, 设备唤醒
     * @retval param1: >=0成功，否则失败
     * @retval str: 设备状态信息
     */
    XSDK_API int CALLBACK XCloudSDK_Device_DevWakeup(int hUser,
                                                     const char* szDevId,
                                                     int nTimeout = 10000,
                                                     int nSeq = 0);

    /**
     * @brief 设备唤醒(支持拓展参数)
     * @param hUser 用户句柄
     * @param szDevId 设备序列号
     * @param szExInfo 拓展参数
     * @param nTimeout 超时时间
     * @param nSeq 自定义值
     * @return >=0接口调用成功，否则失败
     * @retval id: 5214 = EACT_MSG_WAKE_UP, 设备唤醒
     * @retval param1: >=0成功，否则失败
     * @retval str: 设备状态信息
     */
    XSDK_API int CALLBACK XCloudSDK_Device_DevWakeupEx(int hUser,
                                                       const char* szDevId,
                                                       const char* szExInfo = "",
                                                       int nTimeout = 10000,
                                                       int nSeq = 0);

    /**
     * @brief 设备休眠
     * @param hUser 用户句柄
     * @param szDevId 设备序列号
     * @return 无 默认成功
     */
    XSDK_API void CALLBACK XCloudSDK_Device_DevSleep(int hUser, const char* szDevId);

    /**
     * @brief 打开透明串口功能
     * @param hUser 用户句柄
     * @param szDevId 设备序列号
     * @param nSerialType 串口类型0:RS232;1:RS485
     * @param nSeq 用户自定义值
     * @return >0:成功;<=0:失败。对应串口句柄
     * @retval id:12107 = ESXSDK_DEV_TRANSPORT_OPEN，打开透明串口
     * @retval param1 : >=0成功，否则失败
     */
    XSDK_API int CALLBACK XCloudSDK_Device_OpenTransparentSerialPort(int hUser,
                                                                     const char* szDevId,
                                                                     int nSerialType,
                                                                     int nSeq);

    /**
     * @brief 关闭透明串口功能
     * @param nTransHandle 透明串口句柄
     * @return 无
     */
    XSDK_API void CALLBACK XCloudSDK_Device_CloseTransparentSerialPort(int nTransHandle);

    /**
     * @brief 向串口写数据
     * @param hUser 用户句柄
     * @param szDevId 设备序列号
     * @param nSerialType 串口类型0:RS232;1:RS485
     * @param szData 要写入的数据
     * @param nDataLen 数据长度
     * @param nSeq 自定义值
     * @return >=0:成功;<0:失败
     * @retval id:12108 = ESXSDK_DEV_TRANSCOMWRITE，向串口写数据
     * @retval param1 : >=0成功，否则失败
     */
    XSDK_API int CALLBACK XCloudSDK_Device_WriteDataToSerialPort(int hUser,
                                                                 const char* szDevId,
                                                                 int nSerialType,
                                                                 const char* szData,
                                                                 int nDataLen,
                                                                 int nSeq);

    /**
     * @brief 设备端抓图
     * @param hUser 用户句柄
     * @param szDevId 设备序列号
     * @param nChannel 通道号
     * @param sParam 拓展参数
     * @param nSeq 自定义值
     * @return
     */
    XSDK_API int CALLBACK
    XCloudSDK_Device_DevSnap(int hUser, const char* szDevId, int nChannel, const char* sParam = "", int nSeq = 0);

    /**
     * @brief 设备重连使能
     * @param szDevId 设备序列号
     * @param nEnable 是否需要SDK重连，默认重连   0:不需要  1:需要
     * @return
     */
    XSDK_API int CALLBACK XCloudSDK_Device_IsReconnectEnable(const char* szDevId, int nEnable);

    /**
     * @brief 开启上报设备数据
     * @param hUser 用户句柄
     * @param szDevId 设备序列号
     * @param nUploadType 数据上报类型，对应枚举XSDK_UploadDataType
     * @param szUploadType 数据上报具体类型（可选）
     * @param szUploadJson 数据上报协议（可选）
     * @param nSeq 自定义值
     * @return 数据上报功能句柄
     * @retval id:12115 = ESXSDK_DEV_START_UPLOAD_DATA，开启数据上报成功   param1:>=0成功  str:数据上报具体类型
     * @retval id:15005 = ESXSDK_UPLOAD_DEV_DATA, 设备上报的数据  param1:>=0成功  param2:消息ID（暂时无用）
     * param3:数据长度 str:数据上报具体类型  pObject: 数据内容
     */
    XSDK_API int CALLBACK XCloudSDK_Device_StartUploadDevData(int hUser,
                                                              const char* szDevId,
                                                              int nUploadType,
                                                              const char* szUploadType,
                                                              const char* szUploadJson,
                                                              int nSeq);

    /**
     * @brief 停止上报设备数据
     * @param nUploadHandle 数据上报功能句柄，XCloudSDK_Device_StartUploadDevData 接口返回值
     * @return 无
     */
    XSDK_API void CALLBACK XCloudSDK_Device_StopUploadDevData(int nUploadHandle);

    /**
     * @brief 设置设备报警监听器
     * @param hUser 用户句柄
     * @param szDevId 设备ID
     * @return id:1504 = EXCMD_ALARM_REQ，发送报警请求， param1:>=0成功, param2:报警信息json字符串长度
     */
    XSDK_API int CALLBACK XCloudSDK_Device_DevSetAlarmListener(int hUser, const char* szDevId);

    /**
     * @brief 开启AI检测（定制接口功能）
     * @param hUser 用户句柄
     * @param szDevId 设备ID
     * @param szReqJson 请求内容JSON格式
     * @param nTimeout 超时时间
     * @param nSeq 自定义值
     * @return 无
     */
    XSDK_API int CALLBACK XCloudSDK_Device_StartAIListen(int hUser,
                                                         const char* szDevId,
                                                         const char* szReqJson,
                                                         int nTimeout = 5000,
                                                         int nSeq = 0);

    /**
     * @brief 停止AI检测
     * @param nListenHandle 检测句柄---开启检测接口返回值
     * @return 无
     */
    XSDK_API void CALLBACK XCloudSDK_Device_StopAIListen(int nListenHandle);

    /**
     * @brief 强制视频I帧,强制生成一个视频关键帧
     * @param szDevId 设备ID
     * @param nChannel 通道号
     * @param nStreamType 码流类型(0主码流,1子码流)
     * @return
     */
    XSDK_API int CALLBACK XCloudSDK_Device_MakeKeyFrame(const char* szDevId, int nChannel, int nStreamType);

    /**
     * @brief 影子服务器获取设备配置
     * @param hUser 用户句柄
     * @param szReqJson 请求的配置json数组，支持多个配置同时获取
     * @param nTimeout 超时时间
     * @param nSeq 用户自定义值
     * @return 异步回调消息：id:EMSG_BCLOUD365_SHADOW_SERVER_GET_DEV_CFGS = 110000,
     *                    param1: >=0 成功，否则失败,
     *                    Str:返回结果
     * JSON格式示例
     * {
     *      "sn" : "aaaaaaaa",
     *      "confs":["cpu","men","power","wsl","lpwt"]
     * }
     */
    XSDK_API int CALLBACK XCloudSDK_Device_GetConfigFromShadowServer(int hUser,
                                                                     const char* szReqJson,
                                                                     int nTimeout = 5000,
                                                                     int nSeq = 0);

    /**
     * @brief 设置设备离线配置到影子服务
     * @param hUser 用户句柄
     * @param szReqJson 设备配置信息
     * @param nTimeout 超时时间
     * @param nSeq 用户自定义值
     * 示例：
     * {
     *      "sn":"xxx",
     *      "data":{
     *      "NetWork.NetFTP":{
     *          "Enable":false,
     *             "Server":{
     *             "Address":"0x00000000",
     *             "Port":21,
     *             "Name":"FTP"
     *             }
     *         }
     *      }
     * }
     * @return 异步回调消息：id:EMSG_BCLOUD365_SHADOW_SERVER_SET_DEV_CFGS = 110001,
     *                    param1: >=0 成功，否则失败,
     *                    Str:返回结果
     * @remark 如果是设备配置且设备在线时，该设置无效
     */
    XSDK_API int CALLBACK XCloudSDK_Device_SetConfigByShadowServer(int hUser,
                                                                   const char* szReqJson,
                                                                   int nTimeout = 5000,
                                                                   int nSeq = 0);

    /**
     * @brief 设置通道水印
     * @param hUser 用户句柄
     * @param szDevId 设备序列号
     * @param nFontWidth 点阵宽
     * @param nFontHeight 点阵高
     * @param pTitleLattice 点阵数据
     * @param nTitleLatticeLen 点阵数据长度
     * @param nTimeout 超时时间
     * @param nSeq 自定义值
     * @return
     */
    XSDK_API int CALLBACK XCloudSDK_Device_SetTitleByLattice(int hUser,
                                                             const char* szDevId,
                                                             int nFontWidth,
                                                             int nFontHeight,
                                                             const char* pTitleLattice,
                                                             int nTitleLatticeLen,
                                                             int nTimeout = 15000,
                                                             int nSeq = 0);

    /**
     * @brief 开始查询二维码配网结果
     * @param hUser 用户句柄
     * @param szRandom 二维码配网随机码
     * @param nHeartbeat 心跳时间(多久查询一次，范围1s~30s,默认4s)
     * @param nTimeout 超时时间(总查询时长，默认180s)
     * @param nSeq 用户自定义值
     * @return 二维码配网操作句柄
     * 异步回调消息(会回调多次)：id:EMSG_DIS_QR_RESULT = 5114, param1: >=0 成功，否则失败, Str:返回结果
     */
    XSDK_API int CALLBACK XCloudSDK_Device_QRStartQueryResult(int hUser,
                                                              const char* szRandom,
                                                              int nHeartbeat = 4,
                                                              int nTimeout = 180,
                                                              int nSeq = 0);

    /**
     * @brief 停止查询二维码配网结果
     * @param hQRHandle 二维码配网操作句柄
     * @return >=0 成功，否则失败
     */
    XSDK_API int CALLBACK XCloudSDK_Device_QRStopQueryResult(int hQRHandle);

#ifndef OS_WINDOWS
    /**
     * @brief [Asyn]开始配网
     * @param hUser 用户句柄
     * @param ssid 用户id
     * @param data wifi信息 例:S:F11-001P:1234567890T:1
     * @param info 网络信息 例:gateway:*********** ip:***********73 submask:************* dns1:***********
     * dns2:0.0.0.0 mac:02:00:00:00:00:00
     * @param ipaddr 默认网关
     * @param type 加密模式
     * @param isbroad 路由连接速度
     * @param wifiMac 路由mac地址：以“：”分隔的16进制形式
     * @param nTimeout 超时时间
     * @param test 决定配置数据发送调用接口
     * @param nSeq 自定义值
     * @result[Asyn] id:EDIS_DEV_AP_CONFIG = 5106 ; ==0：成功；<0：失败，详见错误码说明;  str:配网返回设备信息
     */
    XSDK_API int CALLBACK XCloudSDK_Device_StartDistribute(int hUser,
                                                           const char* ssid,
                                                           const char* data,
                                                           const char* info,
                                                           const char* ipaddr,
                                                           int type,
                                                           int isbroad,
                                                           const unsigned char wifiMac[6],
                                                           int nTimeout = 100000,
                                                           int test = 0,
                                                           int nSeq = 0);

    /**
     * @brief [同步]停止配网
     */
    XSDK_API void CALLBACK XCloudSDK_Device_StopDistribute();

    typedef enum SDK_WIFIEncryptType
    {
        SDK_WIFI_AUTHMODE_OPEN,
        SDK_WIFI_AUTHMODE_WEP,
        SDK_WIFI_AUTHMODE_WPA_PSK,
        SDK_WIFI_AUTHMODE_WPA2_PSK,
        SDK_WIFI_AUTHMODE_WPA_WPA2_PSK,
        SDK_WIFI_AUTHMODE_WPA2_ENTERPRISE,
        SDK_WIFI_AUTHMODE_WPA3_PSK,
        SDK_WIFI_AUTHMODE_WPA2_WPA3_PSK,
        SDK_WIFI_AUTHMODE_WAPI_PSK,
        SDK_WIFI_AUTHMODE_MAX,
    } SDK_WIFIEncryptType;

    typedef enum SDK_DisDisError
    {
        SDK_ERROR_DIS_BT_CONNECT = -1007,        // 蓝牙连接失败
        SDK_ERROR_DIS_BT_DISCONNECT = -1009,     // 蓝牙断线
        SDK_ERROR_DIS_NOT_FOND_HOT_SPOT = -1023, // 未找到热点
        SDK_ERROR_DIS_HANDSHAKE = -1024,         // 握手失败
        SDK_ERROR_DIS_ROUTER_PWD = -1025,        // 路由器密码错误
        SDK_ERROR_DIS_PARSE_SEND_DATA = -1026,   // 客户端发送的数据解析异常
        SDK_ERROR_DIS_ABNORMAL = -1027,          // 配网失败-未知错误
    } SDK_DisDisError;

    /**
     * @brief 搜索蓝牙
     * @param hUser 接收蓝牙搜索结果的对象
     * @param nSeq 用户自定义值
     * @param nSearchTime 搜索时间，单位秒
     * @return 回调接收结果
     *         消息ID:5107;通知蓝牙搜索开始
     *         消息ID:5108;通知蓝牙搜索结束---默认搜索10s。可主动停止搜索XCloudSDK_Device_CancelSerarchBT
     *         消息ID:5109;param1:结果值;Str中接收设备蓝牙的信息
     */
    XSDK_API int CALLBACK XCloudSDK_Device_StartSearchBT(int hUser, int nSeq, int nSearchTime = 10);

    /**
     * @brief 取消搜索蓝牙
     * @return
     */
    XSDK_API int CALLBACK XCloudSDK_Device_CancelSearchBT();

    /**
     * @brief 开始蓝牙配网
     * @param hUser 接收配网结果对象
     * @param szJsonParam 配网参数JSON格式
     * @param nSeq 用户自定义值
     * @return 返回>0:配网句柄;<=0:失败
     * @result 回调接收结果
     *         蓝牙连接结果消息ID:5110;param1:>=0:成功;<0失败
     *         蓝牙断线通知消息ID:5111
     *         蓝牙发送数据结果消息ID:5113;param1:>=0:成功;<0失败
     *         蓝牙配网结果消息ID:5112;param1:>=0:成功;<0失败;Str:JSON格式结果内容
     * @remark 请求JSON格式
     *  {
     *     "WifiSsid": "xxxxx",     // wifi的ssid
     *     "WifiPwd": "xxxxx",      // wifi的密码
     *     "WifiEncrypetType" : 4,  // wifi加密类型SDK_WIFIEncryptType
     *     "BluetoothUuid" : ""     // 蓝牙的uuid---接口XCloudSDK_Device_StartSerarchBT回调返回
     *  }
     *  返回JSON格式
     * {
     *    "IP": "xx.xx.xx.xx",          // 设备的IP
     *    "Mac": "xx:xx:xx:xx:xx:xx",   // 设备的Mac地址
     *    "RandomPassword": "xxxx",     // 设备的随机用户名
     *    "RandomUser": "xxxx",         // 设备的随机密码
     *    "SerialNum": "xxxxx",         // 设备的序列号
     *    "Token": "xxxxx"              // 设备的Token
     *  }
     */
    XSDK_API SDK_HANDLE CALLBACK XCloudSDK_Device_StartBTDistribute(int hUser, const char* szJsonParam, int nSeq);

    /**
     * @brief 停止蓝牙配网
     * @param nDisHandle 配网句柄---XCloudSDK_Device_StartBTDistribute接口返回值
     */
    XSDK_API void CALLBACK XCloudSDK_Device_StopBTDistribute(SDK_HANDLE nDisHandle);

    /**
     * @brief 蓝牙连接
     * @param szBTUuid 蓝牙的uuid
     * @param nSeq 用户自定义数据
     */
    XSDK_API SDK_HANDLE CALLBACK XCloudSDK_Device_BTConnect(int hUser, const char* szBTUuid, int nSeq);

    /**
     * @brief 蓝牙断开连接
     * @param nHandle 蓝牙句柄---XCloudSDK_Device_BTConnect接口返回值
     */
    XSDK_API void CALLBACK XCloudSDK_Device_BTDisconnect(SDK_HANDLE nHandle);

    /**
     * @brief 发送蓝牙连接数据
     * @param nSendHandle 蓝牙句柄---XCloudSDK_Device_BTConnect接口返回值
     * @param szData 蓝牙连接数据
     */
    XSDK_API void CALLBACK XCloudSDK_Device_BTSendData(SDK_HANDLE nSendHandle, const char* szData);

#endif

#ifdef __cplusplus
}
#endif
#endif // XCDEVICE_H
