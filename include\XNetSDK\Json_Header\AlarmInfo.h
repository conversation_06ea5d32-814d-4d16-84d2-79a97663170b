﻿#pragma once
#include "XNetSDK/Json_Header/JObject_NetSDK.h"

NS_NETSDK_CFG_BEGIN

#define JK_AlarmInfo "AlarmInfo"
/**
 * 报警信息
 * - 报警信息1
 * - 报警信息2
 *   ```cpp
 *      int main(int argc, char* argv[]) {
 *          return 0;
 *      }
 *   ```
 *
 */
class AlarmInfo: public JObject
{
public:
    JIntObj Channel;   ///< `通道`
    JStrObj Event;     ///< 事件
    JStrObj StartTime; ///< 开始时间
    JStrObj Status;    ///< 状态
    JStrObj ExtInfo;   ///< 扩展信息

public:
    /**
     * @brief Construct a new Alarm Info object
     *
     * @param pParent 父对象
     * @param szName  名称
     */
    AlarmInfo(JObject* pParent = NULL, const char* szName = JK_AlarmInfo)
        : JObject(pParent, szName)
        , Channel(this, "Channel")
        , Event(this, "Event")
        , StartTime(this, "StartTime")
        , Status(this, "Status")
        , ExtInfo(this, "ExtInfo"){};

    ~AlarmInfo(void){};
};

NS_NETSDK_CFG_END