#ifndef CAPTUREMANAGER_H
#define CAPTUREMANAGER_H

#include <QObject>
#include <QString>
#include <QDateTime>
#include <QDir>
#include <QFile>
#include <QCoreApplication>
#include <QDebug>
#include <QMap>
#include <QMutex>

// 抓图类型枚举
enum class CaptureType {
    Manual,     // 手动抓图
    Alarm       // 报警抓图
};

// 抓图管理器类
class CaptureManager : public QObject
{
    Q_OBJECT

public:
    explicit CaptureManager(QObject *parent = nullptr);
    
    // 执行抓图
    static bool performCapture(int playHandle, 
                              const QString& deviceId, 
                              const QString& deviceName,
                              CaptureType captureType = CaptureType::Manual);
    
    // 生成抓图文件路径
    static QString generateCapturePath(const QString& deviceId, 
                                     const QString& deviceName,
                                     CaptureType captureType);
    
    // 创建抓图目录
    static bool createCaptureDirectory(const QString& dirPath);
    
    // 将字符串转换为安全的文件名
    static QString toSafeFileName(const QString& input);

    // 处理抓图完成后的文件移动
    static QString handleCaptureComplete(const QString& tempFilePath);

private:
    // 生成随机字符串
    static QString generateRandomString();
    
    // 生成时间戳
    static QString generateTimestamp();

    // 静态映射：临时文件路径 -> 最终文件路径
    static QMap<QString, QString> s_tempToFinalPathMap;
    static QMutex s_pathMapMutex;
};

#endif // CAPTUREMANAGER_H
