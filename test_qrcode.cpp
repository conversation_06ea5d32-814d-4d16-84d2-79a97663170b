#include "qrcodedetector.h"
#include <QCoreApplication>
#include <QDebug>

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);

    QRCodeDetector detector;

    // 测试初始化（ZXing不需要模型文件）
    if (detector.initialize()) {
        qDebug() << "ZXing QR code detector initialized successfully";
    } else {
        qDebug() << "Failed to initialize ZXing QR code detector:" << detector.getLastError();
        return -1;
    }

    // 如果有图片参数，测试检测
    if (argc > 1) {
        QString imagePath = QString::fromLocal8Bit(argv[1]);
        qDebug() << "Testing ZXing QR code detection on:" << imagePath;

        QStringList results = detector.detectQRCodes(imagePath);

        if (results.isEmpty()) {
            qDebug() << "No QR codes detected by ZXing";
        } else {
            qDebug() << "ZXing detected" << results.size() << "QR codes:";
            for (int i = 0; i < results.size(); ++i) {
                qDebug() << "QR code" << (i + 1) << ":" << results[i];
            }
        }
    } else {
        qDebug() << "Usage: test_qrcode <image_path>";
        qDebug() << "This will test ZXing QR code detection";
    }

    return 0;
}
