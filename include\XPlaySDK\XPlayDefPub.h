﻿#pragma once
#ifndef XPlayDefPub_H
#define XPlayDefPub_H

#ifdef OS_WINDOWS
#else
typedef void* HDC;
#endif // OS_WINDOWS

typedef struct _XPlayRECT
{
    int left;
    int top;
    int right;
    int bottom;
} XPlayRECT;

typedef enum EUIDOWNLOAD_STEP
{
    EUIDOWNLOAD_STEP_NONE = 0,      //< 未开始
    EUIDOWNLOAD_STEP_DOWN = 1,      //< 正在下载
    EUIDOWNLOAD_STEP_TRAN = 2,      //< 正在转换文件格式
    EUIDOWNLOAD_STEP_COMPELTE = 10, //< 下载完成
} EUIDOWNLOAD_STEP;

typedef enum EUIMSG
{
    EUIMSG_PLAY_START = 30000, // 开始播放
    EUIMSG_PLAY_PAUSE = 30001, // 暂停播放
    EUIMSG_PLAY_STOP = 30002,  // 播放结束(数据异常停止返回或文件播放结束返回)
    EUIMSG_START_BUFFER_DATA = 30003, // 开始缓存数据---param1对应PlayBufferingState，多窗口同步播放时返回具体状态
    EUIMSG_END_BUFFER_DATA = 30004,       // 缓存结束,开始播放
    EUIMSG_PLAY_MEDIA_FRAME_LOSS = 30005, // 超过4S没有收到数据
    EUIMSG_PLAY_INFO = 30006,             // 播放信息
    EUIMSG_PLAY_SAVE_IMAGE_FILE = 30007,  // 本地保存图片
    EUIMSG_PLAY_CATCH_PIC_BUFFER = 30008, // 获取图片数据
    EUIMSG_AUDIO_DATA = 30009,            // 采集到的音频数据
    EUIMSG_YUV_DATA = 30010,              // YUV数据
    EUIMSG_RECORD_START = 30011,          // 开始录像
    EUIMSG_RECORD_STOP = 30012,           // 录像结束
    /// <summary>
    /// 文件下载进度
    /// param1: 详见EUIDOWNLOAD_STEP
    ///         <0:遇到错误结束下载
    /// param2:进度(0~100)或错误码
    /// param3:当前下载的时间戳
    /// </summary>
    EUIMSG_ON_FILE_DOWNLOAD = 30013,        // 文件下载
    EUIMSG_PLAY_SOUND = 30014,              // 播放音频
    EUIMSG_DECODE_PCM = 30015,              // PCM数据
    EUIMSG_OPEN_SEND_DATA_CHANNEL = 30016,  // 打开发送数据通道
    EUIMSG_CLOSE_SEND_DATA_CHANNEL = 30017, // 关闭发送数据通道
    EUIMSG_SEND_DATA = 30018,               // 发送数据
    EUIMSG_CAPTURE_AUDIO = 30019,           // 采集音频结果
    EUIMSG_ON_FRAME_USR_DATA = 30020,       // 用户自定义信息帧回调
    EUIMSG_ON_PLAY_NEXT_FRAME = 30021,      // 播放下一帧结果
    EUIMSG_ON_PLAY_PREVIOUS_FRAME = 30022,  // 播放上一帧结果
    EUIMSG_COVERT_FILE_RESULT = 30023,      // 转换结果值
    EUIMSG_COVERT_FILE_PROCESS = 30024,     // 转换文件进度
    EUIMSG_BUFFER_STATE = 30025,            // 缓冲区状态;0:缓冲区不足;1:缓冲区已满
} EUIMSG;

typedef enum XPLAYEMSG
{
    XPLAYEMSG_DESTORY_OBJECT = 5003, // 销毁对象
} XPLAYEMSG;

typedef enum EXPLAY_ERROR
{
    EXPLAY_ERROR_OK = 0,                              ///< 成功

    EXPLAY_ERROR_EGSDKObjState_PWD_Error = -101,      // 1 密码不正确
    EXPLAY_ERROR_EGSDKObjState_Account_Error = -102,  // 2 账号不存在
    EXPLAY_ERROR_EGSDKObjState_Timeout = -103,        // 3 登录超时(网络连接失败)
    EXPLAY_ERROR_EGSDKObjState_Logined = -104,        // 4 账号未登录
    EXPLAY_ERROR_EGSDKObjState_Account_Locked = -105, // 5 账号已登录
    EXPLAY_ERROR_EGSDKObjState_Account_Black = -106,  // 6 账号别列为黑名单
    EXPLAY_ERROR_EGSDKObjState_DevSystemBusy = -107,  // 7 设备资源不足
    EXPLAY_ERROR_EGSDKObjState_NetError = -109,       // 9 找不到网络主机
    EXPLAY_ERROR_EGSDKObjState_DELETED = -120,        // 设备不存在（被删除掉了）
    EXPLAY_ERROR_EGSDKObjState_Token_Error = -137,    // 设备token不合法

    // 网络相关错误码
    EXPLAY_ERROR_NET = -1000,              ///< 网络错误
    EXPLAY_ERROR_SENDBUF_FULL = -1001,     ///< 发送缓冲满了
    EXPLAY_ERROR_SEND = -1002,             ///< 发送异常
    EXPLAY_ERROR_RECV = -1003,             ///< 接收异常
    EXPLAY_ERROR_NET_ACTIVE_TEST = -1004,  ///< 超时没有活动就断线了
    EXPLAY_ERROR_NET_NO_OBJECT = -1005,
    EXPLAY_ERROR_NET_CREATE = -1006,
    EXPLAY_ERROR_NET_CONNECT = -1007,      ///< 连接异常
    EXPLAY_ERROR_NET_TIMEOUT = -1008,      ///< 网络超时
    EXPLAY_ERROR_NET_NOCONNECT = -1009,    ///< 没有连接
    EXPLAY_ERROR_NET_SOCKET = -1010,       ///< socket异常
    EXPLAY_ERROR_NET_SOCKET_CLOSE = -1011, ///< socket关闭失败
    EXPLAY_ERROR_NET_NEW_BUFFER = -1012,   ///< New地址失败
    EXPLAY_ERRORE_NET_BUS = -1013,         ///< 网络繁忙
    EXPLAY_ERROR_NET_LISTEN = -1014,       ///< 监听异常
    EXPLAY_ERROR_NET_ACCEPT = -1015,       ///< 接收异常
    EXPLAY_ERROR_NET_NOBUFS = -1016,       ///< 没有内存
    EXPLAY_ERROR_NET_OR_DNS = -1017,       ///< 网络错误或DNS配置错误
    EXPLAY_ERROR_NO_RIGHT = -1018,         ///< 无权限

    /// 常见错误码
    EXPLAY_ERROR_OBJ_NOT_EXIST = -1239510, ///< 句柄无效
    EXPLAY_ERROR_VALUE_NOT_EXIST = -1239511,
    EXPLAY_ERROR_ERROR = -100000,
    EXPLAY_ERROR_PARAM_ERROR = -99999,
    EXPLAY_ERROR_CREATE_FILE = -99998,           ///< 创建文件失败
    EXPLAY_ERROR_OPEN_FILE = -99997,             ///< 打开文件失败
    EXPLAY_ERROR_WRITE_FILE = -99996,            ///< 写文件失败
    EXPLAY_ERROR_READ_FILE = -99995,             ///< 读文件失败
    EXPLAY_ERROR_NO_SUPPORTED = -99994,          ///< 不支持
    EXPLAY_ERROR_OBJ_EXIST = -99992,             ///< 对象不存在
    EXPLAY_ERROR_TIMEOUT = -99991,               ///< 超时
    EXPLAY_ERROR_NOT_FOUND = -99990,             ///< 没找到
    EXPLAY_ERROR_NEW_BUFFER = -99989,            ///< new地址失败
    EXPLAY_ERROR_OBJECT_BUSY = -99986,           ///< 对象繁忙
    EXPLAY_ERROR_SERVER_INTERNAL_ERROR = -99985, ///< 服务器内部错误
    EXPLAY_ERROR_SERVER_BIND_PORT = -99984,      ///< 监听端口bind失败（端口被占用）
    EXPLAY_ERROR_SERVER_LISTEN = -99983,         ///< 监听服务器启动失败
    EXPLAY_ERROR_NET_SEND_BUF_FULL = -99982,     ///< 发送缓冲区满了
    EXPLAY_ERROR_NO_BUFFER = -99981,             ///< 缓冲区大小不够或缓冲区满
    EXPLAY_ERROR_PARSER_PTL = -99980,            ///< 协议解析错误
    EXPLAY_ERROR_USER_OR_PWD_ERROR = -99979,     ///< 用户名或密码错误
    EXPLAY_ERROR_USER_HAS_LOGIN = -99978,        ///< 用户已被其他地方登陆
    EXPLAY_ERROR_USER_LOCKED = -99977,           ///< 用户被锁定
    EXPLAY_ERROR_USER_IN_BLACKLIST = -99976,     ///< 用户列为黑名单
    EXPLAY_ERROR_OFF_LINE = -99975,              ///< 离线状态
    EXPLAY_ERROR_USER_CANCEL = -90000,           ///< 用户取消失败
    EXPLAY_ERROR_FILE_IS_ILLEGAL = -90001,       ///< 文件非法

    EXPLAY_ERROR_YUVDATA_ERROR = -79999,         ///< YUV数据异常
    EXPLAY_ERROR_PLAY_SOUND = -79998,            ///< 打开音频失败（设备不支持音频播放）

    EXPLAY_ERROR_PARAM_ENCODE_FORMAT = -500000,  // 参数编码格式错误（如要求格式为UTF8，但传入gbk）
    EXPLAY_ERROR_PARAM_NOT_JSON = -500001,       // 参数不是JSON格式

    // URL回放错误码
    EXPLAY_ERROR_PLAYBACK_CHANNEL_IN_USE = -514053, // 回放通道已在使用

    // 代理服务类错误，例如Onvif
    EXPLAY_ERROR_Proxy_Offline = -515000,           // 设备离线
    EXPLAY_ERROR_Proxy_NoDevice = -515001,          // 设备没有上报过
    EXPLAY_ERROR_Proxy_ChannelFail = -515002,       // 通道不符合
    EXPLAY_ERROR_Proxy_ChannelOffline = -515003,    // 通道不在线
    EXPLAY_ERROR_Proxy_ProxyAccountError = -515004, // 账号错误

    EXPLAY_ERROR_ProxyInvalidParams = -515100,      // 参数错误
    EXPLAY_ERROR_ProxyInvalidHandle = -515101,      // 句柄错误
    EXPLAY_ERROR_ProxyHttpReq = -515102,            // api 请求失败
    EXPLAY_ERROR_ProxyPlayType = -515103,           // 播放类型错误
    EXPLAY_ERROR_ProxyReqDsmFailed = -515104,       // 向DSM服务请求设备信息失败
    EXPLAY_ERROR_ProxyNotRegister = -515105,        // onvif ssid还没有注册上来

    // 国标相关错误码
    EXPLAY_ERROR_EGSDK_DevServiceUnavailable =
        -515200, // 国标预览/回放由于超负载或维护问题，server或gateway不能处理请求，
                 // 对应sip错误码 503
    EXPLAY_ERROR_EGSDK_PlayNotFound = -515201,   // 国标预览/回放返回Not Found， 对应sip错误码 404
    EXPLAY_ERROR_EGSDK_PlayFailed = -515202,     // 国标预览/回放失败
    EXPLAY_ERROR_EGSDK_PlayReqTimeout = -515203, // 国标预览/回放请求发送设备超时没有回复，
                                                 // 设备超时没有回复设备预览/回放请求

    // RTSP交互相关错误码
    EXPLAY_ERROR_RTSP_PROTOCOL = -516100,
    EXPLAY_ERROR_RTSP_PROTOCOL_FORMAT = -516101,       // URL格式错误
    EXPLAY_ERROR_RTSP_PROTOCOL_NO_RECORD = -516102,    // 没有录像
    EXPLAY_ERROR_RTSP_PROTOCOL_EXPIRE = -516103,       // URL过期
    EXPLAY_ERROR_RTSP_PROTOCOL_AUTHEN_FAIL = -516104,  // URL鉴权失败
    EXPLAY_ERROR_RTSP_PROTOCOL_CAPS = -516105,         // 没有流量
    EXPLAY_ERROR_RTSP_PROTOCOL_URL_FAIL = -516106,     // 向GWM校验URL失败，通信失败
    EXPLAY_ERROR_RTSP_PROTOCOL_START_FAIL = -516107,   // 播放失败，xmts通信失败
    EXPLAY_ERROR_RTSP_PROTOCOL_RECORD_FAIL = -516108,  // 查询录像失败
    EXPLAY_ERROR_RTSP_PROTOCOL_BAD_SEEKTIME = -516109, // 错误的SeekTime时间
    EXPLAY_ERROR_RTSP_PROTOCOL_URL_NOT_FIND = -516110, // 没有这个url信息
    EXPLAY_ERROR_RTSP_PROTOCOL_TOKEN = -516111,        // token解析失败
    EXPLAY_ERROR_RTSP_PROTOCOL_PAYLOAD = -516112,      // payload失败
    EXPLAY_ERROR_RTSP_PROTOCOL_UPDATE_REDIS = -516113, // 更新到redis失败
    EXPLAY_ERROR_RTSP_PROTOCOL_URL_DISBALE = -516114,  // URL不被允许播放
    EXPLAY_ERROR_RTSP_PROTOCOL_URL_OVERFLOW = -516115, // URL超过允许并发数
} EXPLAY_ERROR;

typedef enum EDECODE_TYPE
{
    EDECODE_REAL_TIME_STREAM0 = 0, // 最实时--适用于IP\AP模式等网络状态很好的情况
    EDECODE_REAL_TIME_STREAM1 = 1, //
    EDECODE_REAL_TIME_STREAM2 = 2, //
    EDECODE_REAL_TIME_STREAM3 = 3, // 中等
    EDECODE_REAL_TIME_STREAM4 = 4, //
    EDECODE_REAL_TIME_STREAM5 = 5, //
    EDECODE_REAL_TIME_STREAM6 = 6, // 最流畅--适用于网络不好,网络波动大的情况
    EDECODE_FILE_STREAM = 100,     // 文件流
} EDECODE_TYPE;
#define EDECODE_REAL_TIME_DEFALUT EDECODE_REAL_TIME_STREAM3

typedef enum STREAM_TYPE
{
    FILE_STREAM,            // 文件流
    URL_REALTIME_STREAM,    // url实时
    URL_RECORD_STREAM,      // url回放
    DEVICE_REALTIME_STREAM, // 设备实时
    DEVICE_RECORD_STREAM,   // 设备回放
} STREAM_TYPE;

// 对象属性
typedef enum EMSGOBJ_ATTR
{
    EMSGOBJ_ATTR_VOLUME = 60000,             // 音量大小
    EMSGOBJ_ATTR_DISPLAY_BCSG = 60001,       // 画面背景信息
    EMSGOBJ_ATTR_DECODETYPE = 60002,         // EDecodeObjectType
    EMSGOBJ_ATTR_IS_SPPORT_HARD_DEC = 60003, // EDecodeObjectType
    EMSGOBJ_ATTR_DISABLE_HARD_DEC = 60004,   // 默认硬解，支持禁用--0:不禁;1:禁用
    EMSGOBJ_ATTR_SET_DRAW_TYPE = 60005,      // 1:DirectX 2:OpenGL 3:GDI
    EMSGOBJ_ATTR_SDK_DRAW = 60006,           // 是否需要SDK进行渲染--0:不渲染 1:渲染，默认为1
    EMSGOBJ_ATTR_RETURN_YUV = 60007,         // 是否需要回调YUV数据--0:不回调 1:回调，默认为0
    EMSGOBJ_ATTR_SUPPORT_WEBRTCAUDIO_AND_GET_REMOTE_TYPE = 60008, // 支持获取远端类型
    EMSGOBJ_ATTR_SET_PLAY_DELAY_TIME =
        60009, // 设置两个视频帧之间最大间隔时间,默认1s。当视频帧间隔超过1s时，会按照1s播放显示---应用场景：AOV视频帧间隔比较大时可以设置需要的值
    EMSGOBJ_ATTR_SET_DATA_LOSS_TIME = 60010,     // 设置数据丢失时间
    EMSGOBJ_ATTR_SKIP_AUDIO_IF_NO_VIDEO = 60011, // 若没有视频帧直接跳过音频帧，测试开关
    EMSGOBJ_ATTR_DIFF_OF_VIDEO_AND_AUDIO = 60012, // 视频帧与音频帧的最大间隔，用于判断是否跳过音频帧
} EMSGOBJ_ATTR;

// 全局属性
typedef enum
{
    EXPLAY_ATTR_ENABLE_DEBUG = 60500,     // 开启调试模式，调试模式下保存原始数据
    EXPLAY_ATTR_ENABLE_AEC_DEBUG = 60501, // 开启回声消除调试模式，调试模式下保存原始数据
} EXPLAY_GLOBAL_ATTR;

typedef enum EDecodeObjectType
{
    EDT_FFMPEG_HARD = 0,
    EDT_FFMPEG_SOFT = 1,
    EDT_HISI = 2,
    EDT_IOS_HARD = 3,
    EDT_ANDROID_HARD = 4,
    EDT_DECODE_TYPE_SIZE = 5,
} EDecodeObjectType;

// 鱼眼信息帧结构体
typedef struct SDK_FishEyeFrameSW
{
    char version; // 版本号(如修改下面成员导致app对新老程序分开处理，要让扩展部将参数设置工具中版本号+1，并修改默认配置)
    char lensType;       // 镜头类型，如枚举E_FISH_LENS_TYPE
    short centerOffsetX; // 圆心偏差横坐标  单位:像素点
    short centerOffsetY; // 圆心偏差纵坐标  单位:像素点
    short radius;        // 半径  单位:像素点
    short imageWidth;    // 圆心校正时的图像宽度  单位:像素点
    short imageHeight;   // 圆心校正时的图像高度  单位:像素点
    char viewAngle;      // 视角  0:俯视   1:平视
    char viewMode;       // 显示模式   0:360VR
    char resv[10];       // 预留
} SDK_FishEyeFrameSW;

// 软校正信息
typedef enum FISHEYE_LENS_TYPE_E
{
    SDK_FISHEYE_LENS_GENERAL = 0,
    SDK_FISHEYE_LENS_360VR = 1,
    SDK_FISHEYE_LENS_360LVR = 2,
    SDK_FISHEYE_LENS_180VR = 3,
    SDK_FISHEYE_LENS_DUAL_360VR = 4,
    SDK_FISHEYE_LENS_DUAL_180VR = 5,
} FISHEYE_LENS_TYPE_E;

// 播放缓冲状态
typedef enum PlayBufferingState
{
    PLAY_BUFFERING_STATE_NONE,           // 初始状态
    PLAY_BUFFERING_STATE_WAIT_TO_PLAY,   // 需要等待最少10s之后才能播放
    PLAY_BUFFERING_STATE_PLAY_SOON,      // 即将开始播放100ms~10s之间
    PLAY_BUFFERING_STATE_PLAY_RIGHT_NOW, // 立刻可以播放小于100ms
} PlayBufferingState;

//
typedef void(CALLBACK* PXSDK_DrawCallBack)(XSDK_HANDLE hObject, HDC hDc, void* pUserData);

#endif // XPlayDefPub_H