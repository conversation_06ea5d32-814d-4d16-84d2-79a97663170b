﻿#pragma once
#include "XNetSDK/Json_Header/JObject_NetSDK.h"
NS_NETSDK_CFG_BEGIN

#define JK_TipShow "TipShow"
class TipShow: public JObject
{
public:
    J<PERSON><PERSON>O<PERSON>j NoBeepTipShow;
    JBoolObj NoEmailTipShow;
    JBoolObj NoFTPTipShow;

public:
    TipShow(JObject* pParent = NULL, const char* szName = JK_TipShow)
        : JObject(pParent, szName)
        , NoBeepTipShow(this, "NoBeepTipShow")
        , NoEmailTipShow(this, "NoEmailTipShow")
        , NoFTPTipShow(this, "NoFTPTipShow"){};

    ~TipShow(void){};
};

NS_NETSDK_CFG_END