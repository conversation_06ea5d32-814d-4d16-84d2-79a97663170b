#!/bin/bash
# macOS OpenCV安装脚本
# 使用Homebrew安装OpenCV并配置项目

set -e

echo "========================================"
echo "macOS OpenCV安装脚本"
echo "========================================"

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
OPENCV_DIR="$PROJECT_ROOT/third_party/opencv"

# 检查Homebrew是否安装
check_homebrew() {
    if ! command -v brew &> /dev/null; then
        echo "❌ Homebrew未安装"
        echo "请先安装Homebrew: https://brew.sh"
        echo "运行以下命令:"
        echo '/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"'
        exit 1
    fi
    echo "✅ Homebrew已安装"
}

# 安装OpenCV
install_opencv() {
    echo "正在安装OpenCV..."
    
    # 更新Homebrew
    echo "更新Homebrew..."
    brew update
    
    # 安装OpenCV
    echo "安装OpenCV..."
    if brew list opencv &> /dev/null; then
        echo "OpenCV已安装，检查版本..."
        brew info opencv
    else
        brew install opencv
    fi
    
    echo "✅ OpenCV安装完成"
}

# 创建符号链接
create_symlinks() {
    echo "创建符号链接..."

    # 查找Homebrew OpenCV路径
    BREW_PREFIX=$(brew --prefix)
    OPENCV_CELLAR="$BREW_PREFIX/Cellar/opencv"

    if [ ! -d "$OPENCV_CELLAR" ]; then
        echo "❌ 未找到OpenCV安装目录: $OPENCV_CELLAR"
        exit 1
    fi

    # 找到最新版本
    OPENCV_VERSION=$(ls "$OPENCV_CELLAR" | sort -V | tail -n 1)
    OPENCV_PATH="$OPENCV_CELLAR/$OPENCV_VERSION"

    echo "找到OpenCV版本: $OPENCV_VERSION"
    echo "OpenCV路径: $OPENCV_PATH"

    # 创建项目OpenCV目录
    mkdir -p "$OPENCV_DIR"

    # 检查OpenCV 4.x的新目录结构
    if [ -d "$OPENCV_PATH/include/opencv4" ]; then
        echo "检测到OpenCV 4.x目录结构"
        # 创建include符号链接，指向opencv4目录
        rm -rf "$OPENCV_DIR/include"
        ln -sf "$OPENCV_PATH/include/opencv4" "$OPENCV_DIR/include"
        echo "✅ 创建include符号链接 (opencv4)"
    elif [ -d "$OPENCV_PATH/include/opencv2" ]; then
        echo "检测到传统OpenCV目录结构"
        # 创建include符号链接
        rm -rf "$OPENCV_DIR/include"
        ln -sf "$OPENCV_PATH/include" "$OPENCV_DIR/include"
        echo "✅ 创建include符号链接"
    else
        echo "❌ 未找到include目录: $OPENCV_PATH/include"
        exit 1
    fi

    # 创建lib符号链接
    if [ -d "$OPENCV_PATH/lib" ]; then
        rm -rf "$OPENCV_DIR/lib"
        ln -sf "$OPENCV_PATH/lib" "$OPENCV_DIR/lib"
        echo "✅ 创建lib符号链接"
    else
        echo "❌ 未找到lib目录: $OPENCV_PATH/lib"
        exit 1
    fi
}

# 验证安装
verify_installation() {
    echo "验证安装..."
    
    # 检查关键文件
    if [ ! -f "$OPENCV_DIR/include/opencv2/opencv.hpp" ]; then
        echo "❌ 未找到opencv.hpp头文件"
        exit 1
    fi
    
    # 检查库文件
    if [ ! -d "$OPENCV_DIR/lib" ] || [ -z "$(ls -A "$OPENCV_DIR/lib")" ]; then
        echo "❌ 未找到库文件"
        exit 1
    fi
    
    # 列出可用的库
    echo "可用的OpenCV库:"
    ls "$OPENCV_DIR/lib" | grep -E "libopencv.*\.(dylib|a)$" | head -10
    
    echo "✅ 安装验证成功"
}

# 显示下一步
show_next_steps() {
    echo ""
    echo "========================================"
    echo "安装完成!"
    echo "========================================"
    echo "OpenCV已安装到: $OPENCV_DIR"
    echo ""
    echo "下一步:"
    echo "1. 运行测试: python3 scripts/test_opencv.py"
    echo "2. 构建项目: python3 scripts/build.py"
    echo "   或者: ./build.sh"
    echo ""
}

# 主函数
main() {
    check_homebrew
    install_opencv
    create_symlinks
    verify_installation
    show_next_steps
}

# 运行主函数
main
