cmake_minimum_required(VERSION 3.16)

project(idcamera VERSION 0.1 LANGUAGES CXX)

set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Windows MSVC特定设置
if(WIN32 AND MSVC)
    # 添加C++17支持的编译器标志
    add_compile_options(/Zc:__cplusplus)
    # 设置UTF-8编码支持
    add_compile_options(/utf-8)
    # 启用严格的C++标准模式（Qt要求）
    add_compile_options(/permissive-)
    # 禁用一些警告
    add_compile_options(/wd4819)  # 禁用字符编码警告
endif()

find_package(QT NAMES Qt6 Qt5 REQUIRED COMPONENTS Widgets Network Concurrent)
find_package(Qt${QT_VERSION_MAJOR} REQUIRED COMPONENTS Widgets Network Concurrent)

# 手动配置OpenCV - 使用项目中的third_party目录（MinGW版本）
set(OpenCV_ROOT "${CMAKE_CURRENT_SOURCE_DIR}/third_party/opencv/opencv")

# 手动设置OpenCV变量
set(OpenCV_FOUND TRUE)
set(OpenCV_VERSION "4.8.0")
set(OpenCV_INCLUDE_DIRS "${OpenCV_ROOT}/include")

# 使用MinGW版本的库文件
set(OpenCV_LIB_DIR "${OpenCV_ROOT}/x64/mingw/lib")
set(OpenCV_DLL_DIR "${OpenCV_ROOT}/x64/mingw/bin")

# 设置需要的OpenCV库（移除微信二维码库，使用标准库）
set(OpenCV_LIBS
    "${OpenCV_LIB_DIR}/libopencv_core480.dll.a"
    "${OpenCV_LIB_DIR}/libopencv_imgproc480.dll.a"
    "${OpenCV_LIB_DIR}/libopencv_imgcodecs480.dll.a"
    "${OpenCV_LIB_DIR}/libopencv_objdetect480.dll.a"
    "${OpenCV_LIB_DIR}/libopencv_dnn480.dll.a"
    # "${OpenCV_LIB_DIR}/libopencv_wechat_qrcode480.dll.a"  # 注释掉微信二维码库
)

# 添加OpenCV包含目录
include_directories(${OpenCV_INCLUDE_DIRS})

message(STATUS "手动配置OpenCV (MinGW版本)")
message(STATUS "OpenCV版本: ${OpenCV_VERSION}")
message(STATUS "OpenCV包含目录: ${OpenCV_INCLUDE_DIRS}")
message(STATUS "OpenCV库目录: ${OpenCV_LIB_DIR}")
message(STATUS "OpenCV DLL目录: ${OpenCV_DLL_DIR}")

message(STATUS "使用OpenCV库")
message(STATUS "OpenCV版本: ${OpenCV_VERSION}")
message(STATUS "OpenCV包含目录: ${OpenCV_INCLUDE_DIRS}")
message(STATUS "OpenCV库: ${OpenCV_LIBS}")

# 设置XCloudSDK路径
set(XCLOUD_SDK_ROOT "${CMAKE_CURRENT_SOURCE_DIR}")
set(XCLOUD_SDK_INCLUDE_DIR "${XCLOUD_SDK_ROOT}/include")
set(XCLOUD_SDK_LIB_DIR "${XCLOUD_SDK_ROOT}/lib")

# 添加XCloudSDK头文件路径
include_directories(${XCLOUD_SDK_INCLUDE_DIR})

# 创建XCloudSDK导入库目标
add_library(XCloudSDK SHARED IMPORTED)
set_target_properties(XCloudSDK PROPERTIES
    IMPORTED_LOCATION "${XCLOUD_SDK_LIB_DIR}/XCloudSDK.dll"
    IMPORTED_IMPLIB "${XCLOUD_SDK_LIB_DIR}/XCloudSDK.lib"
    INTERFACE_INCLUDE_DIRECTORIES "${XCLOUD_SDK_INCLUDE_DIR}"
)

set(PROJECT_SOURCES
        main.cpp
        mainwindow.cpp
        mainwindow.h
        mainwindow.ui
        previewwindow.cpp
        previewwindow.h
        capturemanager.cpp
        capturemanager.h
        qrcodedetector.cpp
        qrcodedetector.h
)

if(${QT_VERSION_MAJOR} GREATER_EQUAL 6)
    qt_add_executable(idcamera
        MANUAL_FINALIZATION
        ${PROJECT_SOURCES}
    )
# Define target properties for Android with Qt 6 as:
#    set_property(TARGET idcamera APPEND PROPERTY QT_ANDROID_PACKAGE_SOURCE_DIR
#                 ${CMAKE_CURRENT_SOURCE_DIR}/android)
# For more information, see https://doc.qt.io/qt-6/qt-add-executable.html#target-creation
else()
    add_executable(idcamera
        ${PROJECT_SOURCES}
    )
endif()

target_link_libraries(idcamera PRIVATE
    Qt${QT_VERSION_MAJOR}::Widgets
    Qt${QT_VERSION_MAJOR}::Network
    Qt${QT_VERSION_MAJOR}::Concurrent
    XCloudSDK
    ${OpenCV_LIBS}
)

# vcpkg会自动处理DLL依赖

# Windows下复制DLL到输出目录
if(WIN32)
    # 复制XCloudSDK DLL
    add_custom_command(TARGET idcamera POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "${XCLOUD_SDK_LIB_DIR}/XCloudSDK.dll"
        $<TARGET_FILE_DIR:idcamera>
        COMMENT "复制XCloudSDK.dll到输出目录"
    )

    # 复制OpenCV DLL (MinGW版本)
    if(OpenCV_FOUND)
        # 复制核心OpenCV DLL（移除微信二维码DLL）
        set(OPENCV_DLLS_TO_COPY
            "libopencv_core480.dll"
            "libopencv_imgproc480.dll"
            "libopencv_imgcodecs480.dll"
            "libopencv_objdetect480.dll"
            "libopencv_dnn480.dll"
            "libopencv_dnn_superres480.dll"
            "libopencv_features2d480.dll"
            "libopencv_flann480.dll"
            # "libopencv_wechat_qrcode480.dll"  # 注释掉微信二维码DLL
            "opencv_videoio_ffmpeg480_64.dll"
        )

        foreach(dll ${OPENCV_DLLS_TO_COPY})
            add_custom_command(TARGET idcamera POST_BUILD
                COMMAND ${CMAKE_COMMAND} -E copy_if_different
                "${OpenCV_DLL_DIR}/${dll}"
                $<TARGET_FILE_DIR:idcamera>
                COMMENT "复制OpenCV DLL: ${dll}"
            )
        endforeach()
    endif()

    # 复制models目录到输出目录
    add_custom_command(TARGET idcamera POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_directory
        "${CMAKE_CURRENT_SOURCE_DIR}/models"
        $<TARGET_FILE_DIR:idcamera>/models
        COMMENT "复制models目录到输出目录"
    )

    # 使用windeployqt自动部署Qt依赖
    find_program(WINDEPLOYQT_EXECUTABLE windeployqt HINTS ${Qt${QT_VERSION_MAJOR}_DIR}/../../../bin)
    if(WINDEPLOYQT_EXECUTABLE)
        add_custom_command(TARGET idcamera POST_BUILD
            COMMAND ${WINDEPLOYQT_EXECUTABLE} --qmldir ${CMAKE_SOURCE_DIR} $<TARGET_FILE:idcamera>
            COMMENT "使用windeployqt部署Qt依赖"
        )
        message(STATUS "Found windeployqt: ${WINDEPLOYQT_EXECUTABLE}")
    else()
        message(WARNING "windeployqt not found. Copying Qt libraries manually.")

        # 手动复制必要的Qt DLL
        set(QT_DLLS_TO_COPY
            "Qt${QT_VERSION_MAJOR}Core.dll"
            "Qt${QT_VERSION_MAJOR}Gui.dll"
            "Qt${QT_VERSION_MAJOR}Widgets.dll"
            "Qt${QT_VERSION_MAJOR}Network.dll"
            "Qt${QT_VERSION_MAJOR}Concurrent.dll"
        )

        # 查找Qt DLL路径
        get_target_property(QT_CORE_LOCATION Qt${QT_VERSION_MAJOR}::Core IMPORTED_LOCATION)
        if(QT_CORE_LOCATION)
            get_filename_component(QT_BIN_DIR ${QT_CORE_LOCATION} DIRECTORY)
            message(STATUS "Qt bin directory: ${QT_BIN_DIR}")

            foreach(dll ${QT_DLLS_TO_COPY})
                add_custom_command(TARGET idcamera POST_BUILD
                    COMMAND ${CMAKE_COMMAND} -E copy_if_different
                    "${QT_BIN_DIR}/${dll}"
                    $<TARGET_FILE_DIR:idcamera>
                    COMMENT "复制Qt DLL: ${dll}"
                )
            endforeach()

            # 复制Qt平台插件
            add_custom_command(TARGET idcamera POST_BUILD
                COMMAND ${CMAKE_COMMAND} -E make_directory
                $<TARGET_FILE_DIR:idcamera>/platforms
                COMMAND ${CMAKE_COMMAND} -E copy_if_different
                "${QT_BIN_DIR}/../plugins/platforms/qwindows.dll"
                $<TARGET_FILE_DIR:idcamera>/platforms/
                COMMENT "复制Qt平台插件"
            )
        endif()
    endif()
endif()

set_target_properties(idcamera PROPERTIES
    WIN32_EXECUTABLE TRUE
)

include(GNUInstallDirs)
install(TARGETS idcamera
    BUNDLE DESTINATION .
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
)

if(QT_VERSION_MAJOR EQUAL 6)
    qt_finalize_executable(idcamera)
endif()
