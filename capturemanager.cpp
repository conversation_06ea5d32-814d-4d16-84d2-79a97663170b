#include "capturemanager.h"
#include "XCloudSDK/XCloudSDK.h"

// 静态成员定义
QMap<QString, QString> CaptureManager::s_tempToFinalPathMap;
QMutex CaptureManager::s_pathMapMutex;

CaptureManager::CaptureManager(QObject *parent)
    : QObject(parent)
{
}

// 执行抓图
bool CaptureManager::performCapture(int playHandle, 
                                   const QString& deviceId, 
                                   const QString& deviceName,
                                   CaptureType captureType)
{
    if (playHandle < 0) {
        qWarning() << "Invalid play handle for capture:" << playHandle;
        return false;
    }
    
    if (deviceId.isEmpty()) {
        qWarning() << "Device ID is empty for capture";
        return false;
    }
    
    // 生成抓图文件路径
    QString filePath = generateCapturePath(deviceId, deviceName, captureType);
    if (filePath.isEmpty()) {
        qWarning() << "Failed to generate capture path";
        return false;
    }
    
    qDebug() << "Performing capture for device:" << deviceId;
    qDebug() << "Capture type:" << (captureType == CaptureType::Alarm ? "ALARM" : "MANUAL");
    qDebug() << "File path:" << filePath;
    
    // 调用SDK抓图接口
    int result = XCloudSDK_Play_MediaSnapImage(playHandle, filePath.toUtf8().constData());
    
    if (result >= 0) {
        qDebug() << "Capture request sent successfully for device:" << deviceId;
        qDebug() << "Image will be saved to:" << filePath;
        return true;
    } else {
        qWarning() << "Failed to send capture request for device:" << deviceId << "Error:" << result;
        return false;
    }
}

// 生成抓图文件路径
QString CaptureManager::generateCapturePath(const QString& deviceId,
                                           const QString& deviceName,
                                           CaptureType captureType)
{
    QString safeDeviceId = toSafeFileName(deviceId);
    QString safeDeviceName = toSafeFileName(deviceName);
    QString timestamp = generateTimestamp();
    QString randomStr = generateRandomString();

    QString baseDir = QCoreApplication::applicationDirPath() + "/captures";
    QString tempDir = baseDir + "/temp";  // 统一使用temp目录
    QString finalDir;
    QString fileName;

    if (captureType == CaptureType::Alarm) {
        // 报警抓图：最终保存到 captures/alarm/ 目录
        finalDir = baseDir + "/alarm";
        fileName = QString("ALARM_%1_%2_%3.jpg").arg(safeDeviceId).arg(randomStr).arg(timestamp);
    } else {
        // 手动抓图：最终保存到 captures/manual/设备名_设备IP/ 目录
        QString deviceFolderName = QString("%1_%2").arg(safeDeviceName).arg(safeDeviceId);
        finalDir = baseDir + "/manual/" + deviceFolderName;
        fileName = QString("%1_%2_%3.jpg").arg(safeDeviceId).arg(randomStr).arg(timestamp);
    }

    // 创建temp目录（SDK使用）
    if (!createCaptureDirectory(tempDir)) {
        qWarning() << "Failed to create temp directory:" << tempDir;
        return QString();
    }

    // 创建最终目录
    if (!createCaptureDirectory(finalDir)) {
        qWarning() << "Failed to create final directory:" << finalDir;
        return QString();
    }

    // 返回temp路径给SDK使用（避免中文路径问题）
    QString tempFilePath = QDir::toNativeSeparators(tempDir + "/" + fileName);

    // 保存最终路径信息（用于后续移动文件）
    QString finalFilePath = QDir::toNativeSeparators(finalDir + "/" + fileName);

    qDebug() << "Generated temp path for SDK:" << tempFilePath;
    qDebug() << "Final path for user:" << finalFilePath;

    // 将临时路径和最终路径的映射关系存储起来
    {
        QMutexLocker locker(&s_pathMapMutex);
        s_tempToFinalPathMap[tempFilePath] = finalFilePath;
    }

    return tempFilePath;
}

// 创建抓图目录
bool CaptureManager::createCaptureDirectory(const QString& dirPath)
{
    QDir dir;
    if (!dir.exists(dirPath)) {
        qDebug() << "Creating capture directory:" << dirPath;
        if (dir.mkpath(dirPath)) {
            qDebug() << "Successfully created capture directory:" << dirPath;
            return true;
        } else {
            qWarning() << "Failed to create capture directory:" << dirPath;
            return false;
        }
    }
    return true;
}

// 将字符串转换为安全的文件名
QString CaptureManager::toSafeFileName(const QString& input)
{
    QString result = input;
    
    // Windows文件系统不允许的字符列表
    QStringList invalidChars = {":", "/", "\\", "*", "?", "\"", "<", ">", "|"};
    
    // 将所有无效字符替换为下划线
    for (const QString& invalidChar : invalidChars) {
        result.replace(invalidChar, "_");
    }
    
    // 移除开头和结尾的空格和点号（Windows不允许）
    result = result.trimmed();
    while (result.endsWith('.')) {
        result.chop(1);
    }
    
    // 如果结果为空，使用默认名称
    if (result.isEmpty()) {
        result = "unknown_device";
    }
    
    return result;
}

// 生成随机字符串
QString CaptureManager::generateRandomString()
{
    return QString::number(QDateTime::currentMSecsSinceEpoch() % 100000);
}

// 生成时间戳
QString CaptureManager::generateTimestamp()
{
    return QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss_zzz");
}

// 处理抓图完成后的文件移动
QString CaptureManager::handleCaptureComplete(const QString& tempFilePath)
{
    QString finalFilePath;

    // 从映射中获取最终路径
    {
        QMutexLocker locker(&s_pathMapMutex);
        if (s_tempToFinalPathMap.contains(tempFilePath)) {
            finalFilePath = s_tempToFinalPathMap.take(tempFilePath);  // 取出并删除映射
        }
    }

    if (finalFilePath.isEmpty()) {
        qWarning() << "No final path mapping found for temp file:" << tempFilePath;
        return tempFilePath;  // 返回原路径
    }

    // 检查临时文件是否存在
    if (!QFile::exists(tempFilePath)) {
        qWarning() << "Temp file does not exist:" << tempFilePath;
        return QString();
    }

    // 如果最终文件已存在，先删除
    if (QFile::exists(finalFilePath)) {
        QFile::remove(finalFilePath);
    }

    // 移动文件到最终位置
    if (QFile::rename(tempFilePath, finalFilePath)) {
        qDebug() << "File moved from temp to final path:" << finalFilePath;
        return finalFilePath;
    } else {
        qWarning() << "Failed to move file from" << tempFilePath << "to" << finalFilePath;
        return tempFilePath;  // 移动失败，返回临时路径
    }
}
