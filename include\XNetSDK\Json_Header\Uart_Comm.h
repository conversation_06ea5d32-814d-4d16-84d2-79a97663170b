﻿#pragma once
#include "XNetSDK/Json_Header/JObject_NetSDK.h"
#include "XNetSDK/Json_Header/ExtraFormat.h"
#include "XNetSDK/Json_Header/MainFormat.h"
NS_NETSDK_CFG_BEGIN

#define JK_Uart_Comm "Uart.Comm"
class Uart_Comm: public JObject
{
public:
    JStrObj ProtocolName;
    JIntObj PortNo;
    JObjArray<JIntObj> Attribute;

public:
    Uart_Comm(JObject* pParent = NULL, const char* szName = JK_Uart_Comm)
        : JObject(pParent, szName)
        , ProtocolName(this, "ProtocolName")
        , PortNo(this, "PortNo")
        , Attribute(this, "Attribute"){};

    ~Uart_Comm(void){};
};

NS_NETSDK_CFG_END