<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/camera_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.camera.view.PreviewView
        android:id="@+id/viewFinder"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:scaleType="fitCenter" />

    <com.example.zxingcppdemo.PreviewOverlay
        android:id="@+id/overlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>

    <TextView
        android:id="@+id/result"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_large"
        android:background="#65000000"
        android:shadowColor="@android:color/black"
        android:shadowRadius="6"
        android:text="@string/unknown"
        android:textAllCaps="false"
        android:textAppearance="@style/TextAppearance.AppCompat.Medium.Inverse"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/fps"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_xsmall"
        android:layout_marginEnd="@dimen/margin_xsmall"
        android:background="#65000000"
        android:shadowColor="@android:color/black"
        android:shadowRadius="6"
        android:text="@string/unknown"
        android:textAllCaps="false"
        android:textAppearance="@style/TextAppearance.AppCompat.Small.Inverse"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Camera control buttons -->

    <ImageButton
        android:id="@+id/capture"
        android:layout_width="@dimen/shutter_button_size"
        android:layout_height="@dimen/shutter_button_size"
        android:layout_marginBottom="@dimen/shutter_button_margin"
        android:background="@drawable/ic_shutter"
        android:contentDescription="@string/capture_button_alt"
        android:scaleType="fitCenter"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <com.google.android.material.chip.ChipGroup
        android:layout_width="120dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="8dp"
        android:scaleX="-1"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <com.google.android.material.chip.Chip
            android:id="@+id/java"
            style="@style/ChipR"
            android:text="Java" />

        <com.google.android.material.chip.Chip
            android:id="@+id/qrcode"
            style="@style/ChipR"
            android:text="QRCode" />

        <com.google.android.material.chip.Chip
            android:id="@+id/tryHarder"
            style="@style/ChipR"
            android:text="tryHarder" />

        <com.google.android.material.chip.Chip
            android:id="@+id/tryRotate"
            style="@style/ChipR"
            android:text="tryRotate" />

        <com.google.android.material.chip.Chip
            android:id="@+id/tryInvert"
            style="@style/ChipR"
            android:text="tryInvert" />

        <com.google.android.material.chip.Chip
            android:id="@+id/tryDownscale"
            style="@style/ChipR"
            android:text="tryDownscale" />

        <com.google.android.material.chip.Chip
            android:id="@+id/multiSymbol"
            style="@style/ChipR"
            android:text="multiSymbol" />

        <com.google.android.material.chip.Chip
            android:id="@+id/crop"
            style="@style/ChipR"
            android:text="crop" />

        <com.google.android.material.chip.Chip
            android:id="@+id/torch"
            style="@style/ChipR"
            android:text="Torch" />

        <com.google.android.material.chip.Chip
            android:id="@+id/pause"
            style="@style/ChipR"
            android:text="pause" />

    </com.google.android.material.chip.ChipGroup>

</androidx.constraintlayout.widget.ConstraintLayout>
