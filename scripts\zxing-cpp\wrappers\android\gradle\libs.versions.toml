[versions]
androidCoreDesugaring = "2.0.3"
androidGradlePlugin = "8.1.4"
androidCompileSdk = "34"
androidMinSdk = "21"
androidTargetSdk = "33"
androidx-activity = "1.8.1"
androidx-appcompat = "1.6.1"
androidx-camera = "1.3.0"
androidx-core = "1.12.0"
androidx-constraintLayout = "2.1.4"
android-material = "1.11.0-rc01"
kotlin = "1.9.10"
zxing-core = "3.5.2"

[libraries]
androidx-activityktx = { module = "androidx.activity:activity-ktx", version.ref = "androidx-activity" }
androidx-appcompat = { module = "androidx.appcompat:appcompat", version.ref = "androidx-appcompat" }
androidx-camera-core = { module = "androidx.camera:camera-core", version.ref = "androidx-camera" }
androidx-camera-camera2 = { module = "androidx.camera:camera-camera2", version.ref = "androidx-camera" }
androidx-camera-lifecycle = { module = "androidx.camera:camera-lifecycle", version.ref = "androidx-camera" }
androidx-camera-view = { module = "androidx.camera:camera-view", version.ref = "androidx-camera" }
androidx-constraintlayout = { module = "androidx.constraintlayout:constraintlayout", version.ref = "androidx-constraintLayout" }
androidx-core = { module = "androidx.core:core-ktx", version.ref = "androidx-core" }
android-material = { module = "com.google.android.material:material", version.ref = "android-material" }
zxing-core = { module = "com.google.zxing:core", version.ref = "zxing-core" }

[plugins]
android-application = { id = "com.android.application", version.ref = "androidGradlePlugin" }
android-library = { id = "com.android.library", version.ref = "androidGradlePlugin" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }

