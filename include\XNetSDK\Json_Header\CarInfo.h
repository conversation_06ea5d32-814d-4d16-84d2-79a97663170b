﻿#pragma once
#include "XNetSDK/Json_Header/JObject.h"
NS_NETSDK_CFG_BEGIN

#define JK_CarInfo "CarInfo"
class CCarInfo: public JObject
{
public:
    JIntObj LftX;
    JIntObj RgnHgt;
    JIntObj RgnWid;
    JIntObj State;
    JIntObj TopY;
    JStrObj PlateCode;
    JStrObj PlateColor;

public:
    CCarInfo(JObject* pParent = NULL, const char* szName = JK_CarInfo)
        : JObject(pParent, szName)
        , LftX(this, "LftX")
        , RgnHgt(this, "RgnHgt")
        , RgnWid(this, "RgnWid")
        , State(this, "State")
        , TopY(this, "TopY")
        , PlateCode(this, "PlateCode")
        , PlateColor(this, "PlateColor"){};

    ~CCarInfo(void){};
};

NS_NETSDK_CFG_END