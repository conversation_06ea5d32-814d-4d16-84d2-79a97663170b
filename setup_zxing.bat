@echo off
echo Setting up ZXing-cpp library...

REM 检查Python是否可用
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python not found! Please install Python first.
    pause
    exit /b 1
)

REM 运行ZXing设置脚本
python scripts\setup_zxing.py

if %errorlevel% neq 0 (
    echo Failed to setup ZXing-cpp
    pause
    exit /b 1
)

echo ZXing-cpp setup completed successfully!
echo You can now build the project with CMake.
pause
