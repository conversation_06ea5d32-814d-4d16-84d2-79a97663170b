#!/usr/bin/env python3
"""
下载预编译的ZXing-cpp库
用于快速获取ZXing库而无需编译
"""

import os
import sys
import urllib.request
import zipfile
import shutil
from pathlib import Path

def download_file(url, filename):
    """下载文件"""
    print(f"Downloading {filename}...")
    try:
        urllib.request.urlretrieve(url, filename)
        print(f"Downloaded {filename}")
        return True
    except Exception as e:
        print(f"Error downloading {filename}: {e}")
        return False

def extract_zip(zip_file, extract_to):
    """解压ZIP文件"""
    try:
        with zipfile.ZipFile(zip_file, 'r') as zip_ref:
            zip_ref.extractall(extract_to)
        print(f"Extracted {zip_file} to {extract_to}")
        return True
    except Exception as e:
        print(f"Error extracting {zip_file}: {e}")
        return False

def create_zxing_structure():
    """创建ZXing目录结构并下载必要文件"""
    # 创建目录结构
    base_dir = "../third_party/zxing"
    include_dir = os.path.join(base_dir, "include", "ZXing")
    lib_dir = os.path.join(base_dir, "lib")
    bin_dir = os.path.join(base_dir, "bin")
    
    os.makedirs(include_dir, exist_ok=True)
    os.makedirs(lib_dir, exist_ok=True)
    os.makedirs(bin_dir, exist_ok=True)
    
    # 下载ZXing源码以获取头文件
    zxing_url = "https://github.com/zxing-cpp/zxing-cpp/archive/refs/tags/v2.2.1.zip"
    zip_file = "zxing-cpp-2.2.1.zip"
    
    if not download_file(zxing_url, zip_file):
        return False
    
    if not extract_zip(zip_file, "."):
        return False
    
    # 复制头文件
    src_include = "zxing-cpp-2.2.1/core/src"
    if os.path.exists(src_include):
        # 复制所有头文件
        for root, dirs, files in os.walk(src_include):
            for file in files:
                if file.endswith('.h'):
                    src_file = os.path.join(root, file)
                    rel_path = os.path.relpath(src_file, src_include)
                    dst_file = os.path.join(include_dir, rel_path)
                    
                    # 创建目标目录
                    os.makedirs(os.path.dirname(dst_file), exist_ok=True)
                    shutil.copy2(src_file, dst_file)
                    print(f"Copied header: {rel_path}")
    
    # 清理临时文件
    os.remove(zip_file)
    shutil.rmtree("zxing-cpp-2.2.1")
    
    # 创建一个简单的库文件占位符和说明
    readme_content = """# ZXing-cpp Library

This directory contains ZXing-cpp headers but you need to provide the library files.

## Options to get the library files:

### Option 1: Use vcpkg (Recommended)
```bash
vcpkg install zxing-cpp:x64-windows
```
Then copy the library files from vcpkg to this directory.

### Option 2: Download precompiled binaries
Visit: https://github.com/zxing-cpp/zxing-cpp/releases
Download the appropriate release for your platform.

### Option 3: Compile from source
You need CMake installed:
```bash
git clone https://github.com/zxing-cpp/zxing-cpp.git
cd zxing-cpp
mkdir build && cd build
cmake .. -DCMAKE_BUILD_TYPE=Release -DBUILD_SHARED_LIBS=ON
cmake --build . --config Release
```

## Required files for this project:
- lib/: ZXing.lib (or libZXing.a for MinGW)
- bin/: ZXing.dll (for Windows)

## Current status:
- ✓ Headers installed
- ✗ Library files missing - please install using one of the options above
"""
    
    with open(os.path.join(base_dir, "README.md"), 'w') as f:
        f.write(readme_content)
    
    print(f"ZXing headers installed to {base_dir}")
    print("Please install the library files using one of the methods described in README.md")
    
    return True

def main():
    """主函数"""
    # 切换到scripts目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    print("Downloading ZXing-cpp headers...")
    
    if create_zxing_structure():
        print("ZXing-cpp headers downloaded successfully!")
        print("Next steps:")
        print("1. Install CMake if you want to compile from source")
        print("2. Or use vcpkg to install precompiled libraries")
        print("3. Or download precompiled binaries from GitHub releases")
    else:
        print("Failed to download ZXing-cpp")
        sys.exit(1)

if __name__ == "__main__":
    main()
