﻿/*********************************************************************************
 *Description:
 *History:
 Date:    2022.12.16
 Action：Create
 **********************************************************************************/
#pragma once
#ifndef XCLOUDSDK_H
#define XCLOUDSDK_H
#include "XBasic/XSDKPublic.h"
#include "XCloudSDK/XCDevice.h"
#include "XCloudSDK/XCPlay.h"

#ifdef __cplusplus
extern "C"
{
#endif

    /**
     * @brief 设置程序为前台模式（对应后台模式）
     * @param nActive 0：后台；1：前台(激活)
     * @return >=0：成功；<0：失败
     */
    XSDK_API void CALLBACK XCloudSDK_SetActive(int nActive);

    /**
     * @brief XCloudSDK初始化
     * @param sJsonParam 数据内容
     * @return >=0：成功；<0：失败
     */
    XSDK_API int CALLBACK XCloudSDK_Init(const char* sJsonParam);

    /**
     * @brief XCloudSDK反初始化
     * @return
     */
    XSDK_API void CALLBACK XCloudSDK_UnInit();

    /**
     * @brief 设置日志类型和级别
     *
     * @param type: -1: 禁用; 1: 输出到控制台,包括logcat; 2: 输出到文件;
     * @param level: 1: debug; 2: info; 3: warn; 4: error; 5: critical; 6: fatal
     */
    XSDK_API void CALLBACK XCloudSDK_SetLogTypeAndLevel(int type, int level);

#ifdef OS_IOS
    XSDK_API UI_HANDLE CALLBACK XCloudSDK_RegWnd(LP_WND_OBJ pWnd, const char* name = "OnXCloudSDKResult:");
    XSDK_API void CALLBACK XCloudSDK_UnRegWnd(UI_HANDLE hWnd);
#endif

    /**
     * @brief 注册窗口消息
     * @param hWindow 窗口消息通知---通过Message消息
     * @return 用户句柄 >=0：成功；<0：失败
     */
    XSDK_API int CALLBACK XCloudSDK_RegisterWindow(LP_WND_OBJ hWindow);

    /**
     * @brief 注册回调函数
     * @param pMsgCallback 回调函数接收
     * @param pUserData 用户自定义指针，回调函数中返回
     * @return 用户句柄 >=0：成功；<0：失败
     */
    XSDK_API int CALLBACK XCloudSDK_RegisterCallback(PXSDK_MessageCallBack pMsgCallback, void* pUserData);

    /**
     * @brief 销毁注册消息接收
     * @param hUser 用户句柄
     * @return
     */
    XSDK_API void CALLBACK XCloudSDK_UnRegister(int hUser);

    /**
     * @brief 更新鉴权信息
     * @param szPlatUUID        平台客户唯一标志码
     * @param szPlatAppKey      平台应用Key
     * @param szPlatAppSecret   平台应用密钥
     * @param nPlatMovedCard    平台移动取模基数
     * @return >=0：成功；<0：失败
     */
    XSDK_API int CALLBACK XCloudSDK_SetAppParams(const char* szPlatUUID = "",
        const char* szPlatAppKey = "",
        const char* szPlatAppSecret = "",
        int nPlatMovedCard = 0);

    /**
     * @brief 写日志
     * @param szLog 日志内容
     * @return
     */
    XSDK_API void CALLBACK XCloudSDK_Log(const char* szLog);


    /**
     * @brief 获取版本信息
     * @param szVersion 接收版本信息
     * @return
     */
    XSDK_API char* CALLBACK XCloudSDK_GetVersionInfo(char szVersion[512]);

    /**
     * @brief 切换网络类型
     * @param nNetType 网络类型---EXCLOUDSDK_NETWORKTYPE
     * @return
     * id: 6500 = EXCLOUDSDK_CHANGE_NET_TYPE, 切换网络状态
     * param1: 尝试重连次数
     */
    XSDK_API int CALLBACK XCloudSDK_ChangeNetType(int hUser, int nNetType);

    /**
     * @brief 清除本地缓存
     * @param hUser 用户句柄
     * @param nSeq 自定义值
     * @return 回调消息 EXCLOUDSDK_CLEAR_CACHE = 6504   清除本地缓存
     * 清空之前查询到的设备状态/登出所有设备/清除AccessToken等账号相关的信息/断开MQTT连接
     */
    XSDK_API int XCloudSDK_ClearLocalCache(int hUser, int nSeq);

#ifdef __cplusplus
}
#endif
#endif // XCLOUDSDK_H
