# ZXing-cpp 集成指南

## 概述
本项目已将二维码检测从微信二维码检测器改为使用ZXing-cpp库。ZXing是一个开源的多格式1D/2D条码图像处理库。

## 快速开始

### 1. 自动安装ZXing（推荐）
运行提供的安装脚本：
```bash
# Windows
setup_zxing.bat

# 或者直接运行Python脚本
python scripts/setup_zxing.py
```

### 2. 手动安装ZXing

#### 方法A: 使用vcpkg
```bash
# 安装vcpkg (如果还没有安装)
git clone https://github.com/Microsoft/vcpkg.git
cd vcpkg
.\bootstrap-vcpkg.bat

# 安装ZXing-cpp
.\vcpkg install zxing-cpp:x64-windows
```

#### 方法B: 从源码编译
```bash
# 克隆ZXing-cpp仓库
git clone https://github.com/zxing-cpp/zxing-cpp.git
cd zxing-cpp

# 创建构建目录
mkdir build
cd build

# 配置CMake
cmake .. -DCMAKE_BUILD_TYPE=Release -DBUILD_SHARED_LIBS=ON

# 编译
cmake --build . --config Release

# 安装到项目的third_party目录
cmake --install . --prefix ../../../third_party/zxing
```

## 项目结构
```
idcamera/
├── third_party/
│   └── zxing/
│       ├── include/
│       │   └── ZXing/
│       ├── lib/
│       └── bin/
├── scripts/
│   └── setup_zxing.py
├── qrcodedetector.h
├── qrcodedetector.cpp
└── CMakeLists.txt
```

## 代码修改说明

### 主要变更
1. **头文件**: 移除微信二维码模块，添加ZXing支持
2. **检测逻辑**: 使用ZXing的ReadBarcode函数
3. **构建配置**: 更新CMakeLists.txt以链接ZXing库

### ZXing vs 微信二维码检测器对比

| 特性 | 微信二维码检测器 | ZXing |
|------|------------------|-------|
| 模型文件 | 需要4个模型文件 | 不需要 |
| 多格式支持 | 仅QR码 | 支持多种格式 |
| 跨平台 | 依赖OpenCV模块 | 纯C++，跨平台 |
| 性能 | 高精度 | 良好性能 |
| 依赖 | opencv_wechat_qrcode | 独立库 |

## 使用示例

### 基本用法
```cpp
#include "qrcodedetector.h"

QRCodeDetector detector;

// 初始化（不需要模型文件）
if (detector.initialize()) {
    // 检测二维码
    QStringList results = detector.detectQRCodes("image.jpg");
    
    for (const QString& result : results) {
        qDebug() << "Detected QR code:" << result;
    }
}
```

### 从cv::Mat检测
```cpp
cv::Mat image = cv::imread("qrcode.jpg");
QStringList results = detector.detectQRCodes(image);
```

## 编译说明

### 1. 确保ZXing库可用
运行安装脚本或手动安装ZXing库到third_party目录。

### 2. 构建项目
```bash
mkdir build
cd build
cmake ..
cmake --build .
```

### 3. 如果使用vcpkg
```bash
cmake -DCMAKE_TOOLCHAIN_FILE=path/to/vcpkg/scripts/buildsystems/vcpkg.cmake ..
```

## 故障排除

### 常见问题
1. **找不到ZXing库**: 
   - 运行 `setup_zxing.bat` 或 `python scripts/setup_zxing.py`
   - 检查 `third_party/zxing` 目录是否存在

2. **编译错误**: 
   - 确保ZXing头文件路径正确
   - 检查CMake输出中的ZXing配置信息

3. **运行时错误**: 
   - 确保ZXing的DLL文件在可执行文件目录中
   - 检查依赖库是否完整

### 调试提示
- 查看CMake配置输出，确认ZXing被正确找到
- 使用 `ldd`(Linux) 或 `dumpbin`(Windows) 检查可执行文件的依赖
- 检查 `third_party/zxing` 目录结构是否正确

## 优势
1. **无需模型文件**: ZXing不需要额外的模型文件
2. **更好的跨平台支持**: ZXing是纯C++库
3. **多格式支持**: 除了QR码，还支持其他条码格式
4. **活跃维护**: ZXing-cpp是一个活跃维护的开源项目
5. **更简单的部署**: 不需要复制模型文件到目标系统

## 注意事项
- ZXing的检测精度可能与微信二维码检测器略有不同
- 对于特别模糊或损坏的二维码，可能需要调整检测参数
- 确保图像格式正确转换（BGR格式用于ZXing）
