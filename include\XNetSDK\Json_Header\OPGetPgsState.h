﻿#pragma once
#include "XNetSDK/Json_Header/JObject.h"
#include "XNetSDK/Json_Header/PgsTargetInfoAll.h"
NS_NETSDK_CFG_BEGIN

#define JK_OPGetPgsState "OPGetPgsState"
class OPGetPgsState: public JObject
{
public:
    JObjArray<PgsTargetInfoAll> items;

public:
    OPGetPgsState(JObject* pParent = NULL, const char* szName = JK_OPGetPgsState)
        : JObject(pParent, szName)
        , items(this, "PgsTargetInfoAll"){};

    ~OPGetPgsState(void){};
};

NS_NETSDK_CFG_END