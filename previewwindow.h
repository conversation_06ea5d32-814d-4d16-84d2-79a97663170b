#ifndef PREVIEWWINDOW_H
#define PREVIEWWINDOW_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QLabel>
#include <QPushButton>
#include <QSlider>
#include <QComboBox>
#include <QMessageBox>
#include <QDateTime>
#include <QDir>
#include <QStandardPaths>
#include <QTimer>
#include <QFile>

// XCloudSDK 头文件 - 重新启用
#include "XCloudSDK/XCloudSDK.h"
#include "XNetSDK/XNetSDKDefine.h"

// 抓图管理器
#include "capturemanager.h"

// 前向声明
class MainWindow;

class PreviewWindow : public QWidget
{
    Q_OBJECT

public:
    explicit PreviewWindow(const QString& deviceId, const QString& deviceName, QWidget *parent = nullptr);
    ~PreviewWindow();

    bool startPreview();
    void stopPreview();
    bool captureImage();
    bool startRecord();
    bool stopRecord();
    void setVolume(int volume);

    // PTZ控制方法
    void ptzControl(const QString& command, bool stop = false, int step = 4);

private slots:
    void onBackButtonClicked();
    void onCaptureButtonClicked();
    // void onRecordButtonClicked();  // 录像按钮槽函数暂时注释
    void onVolumeChanged(int volume);
    void onStreamTypeChanged(int index);
    void onPtzButtonPressed();
    void onPtzButtonReleased();

    // 线程安全的UI更新槽函数
    void onCaptureResultReceived(int result, const QString& filePath);

private:
    void setupUI();
    void setupPTZControls();
    QWidget* createControlPanel();
    
    // SDK回调处理 - 重新启用
    static int CALLBACK previewCallback(XSDK_HANDLE hObject, int nMsgId, int nParam1, int nParam2,
                                       int nParam3, const char* szString, void* pObject,
                                       int64 lParam, int nSeq, void* pUserData, void* pMsg);

    void handleSDKMessage(XSDK_HANDLE hObject, int nMsgId, int nParam1, int nParam2,
                         int nParam3, const char* szString, void* pObject,
                         int64 lParam, int nSeq);

    // 预览相关方法
    bool startRealPreview();
    void stopRealPreview();

    // 抓图结果处理（在回调线程中调用）
    void handleCaptureResult(int result, const char* filePath);

    // 辅助方法：将字符串转换为安全的文件名
    QString toSafeFileName(const QString& input);

signals:
    // 线程安全的信号，用于从回调线程向主线程传递结果
    void captureResultReady(int result, const QString& filePath);

private:
    QString m_deviceId;
    QString m_deviceName;        // 设备名称
    QWidget* m_previewWidget;    // 视频显示区域
    QLabel* m_statusLabel;
    QLabel* m_infoLabel;         // 提示信息标签
    QPushButton* m_captureBtn;
    QPushButton* m_recordBtn;
    QSlider* m_volumeSlider;
    QComboBox* m_streamTypeCombo;

    // PTZ控制按钮
    QPushButton* m_ptzUpBtn;
    QPushButton* m_ptzDownBtn;
    QPushButton* m_ptzLeftBtn;
    QPushButton* m_ptzRightBtn;
    QPushButton* m_ptzLeftUpBtn;
    QPushButton* m_ptzLeftDownBtn;
    QPushButton* m_ptzRightUpBtn;
    QPushButton* m_ptzRightDownBtn;
    QPushButton* m_ptzResetBtn;

    // 焦距/光圈控制按钮
    QPushButton* m_irisSmallBtn;
    QPushButton* m_irisLargeBtn;
    QPushButton* m_focusNearBtn;
    QPushButton* m_focusFarBtn;
    QPushButton* m_zoomWideBtn;
    QPushButton* m_zoomTileBtn;

    // SDK相关
    int m_hUser;
    int m_playHandle;
    bool m_isRecording;
    bool m_isPreviewActive;

    // 抓图相关（现在由CaptureManager统一处理）
};

#endif // PREVIEWWINDOW_H
