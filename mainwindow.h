#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QStackedWidget>
#include <QDialog>
#include <QLineEdit>
#include <QFormLayout>
#include <QPushButton>
#include <QWidget>
#include <QLabel>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QSlider>
#include <QComboBox>
#include <QCloseEvent>
#include <QMutex>
#include <QMutexLocker>
#include <QThreadPool>
#include <QRunnable>
#include <QSemaphore>
#include <QTimer>

// XCloudSDK 头文件
#include "XCloudSDK/XCloudSDK.h"

// 抓图管理器
#include "capturemanager.h"

// 二维码检测器
#include "qrcodedetector.h"

// 前向声明
class QResizeEvent;
class QShowEvent;
class PreviewWindow;

QT_BEGIN_NAMESPACE
namespace Ui {
class MainWindow;
}
QT_END_NAMESPACE

// 自定义设备容器类，用于处理删除按钮的位置
class DeviceContainerWidget : public QWidget
{
    Q_OBJECT

public:
    explicit DeviceContainerWidget(QWidget *parent = nullptr);
    void setDeleteButton(QPushButton *button);

protected:
    void resizeEvent(QResizeEvent *event) override;
    void showEvent(QShowEvent *event) override;

private:
    QPushButton *deleteButton;
    void updateDeleteButtonPosition();
};

// 实时预览窗口类 - 暂时注释掉，等解决编译问题后再启用
/*
class PreviewWindow : public QWidget
{
    Q_OBJECT

public:
    explicit PreviewWindow(const QString& deviceId, QWidget *parent = nullptr);
    ~PreviewWindow();

    bool startPreview();
    void stopPreview();
    bool captureImage();
    bool startRecord();
    bool stopRecord();
    void setVolume(int volume);

    // PTZ控制方法
    void ptzControl(const QString& command, bool stop = false, int step = 4);

private slots:
    void onBackButtonClicked();
    void onCaptureButtonClicked();
    void onRecordButtonClicked();
    void onVolumeChanged(int volume);
    void onStreamTypeChanged(int index);
    void onPtzButtonPressed();
    void onPtzButtonReleased();

private:
    void setupUI();
    void setupPTZControls();
    QWidget* createControlPanel();

    // SDK回调处理
    static int CALLBACK previewCallback(XSDK_HANDLE hObject, int nMsgId, int nParam1, int nParam2,
                                       int nParam3, const char* szString, void* pObject,
                                       int64 lParam, int nSeq, void* pUserData, void* pMsg);

    void handleSDKMessage(XSDK_HANDLE hObject, int nMsgId, int nParam1, int nParam2,
                         int nParam3, const char* szString, void* pObject,
                         int64 lParam, int nSeq);

    QString m_deviceId;
    QWidget* m_previewWidget;
    QLabel* m_statusLabel;
    QPushButton* m_captureBtn;
    QPushButton* m_recordBtn;
    QSlider* m_volumeSlider;
    QComboBox* m_streamTypeCombo;

    // PTZ控制按钮
    QPushButton* m_ptzUpBtn;
    QPushButton* m_ptzDownBtn;
    QPushButton* m_ptzLeftBtn;
    QPushButton* m_ptzRightBtn;
    QPushButton* m_ptzLeftUpBtn;
    QPushButton* m_ptzLeftDownBtn;
    QPushButton* m_ptzRightUpBtn;
    QPushButton* m_ptzRightDownBtn;
    QPushButton* m_ptzResetBtn;

    // 焦距/光圈控制按钮
    QPushButton* m_irisSmallBtn;
    QPushButton* m_irisLargeBtn;
    QPushButton* m_focusNearBtn;
    QPushButton* m_focusFarBtn;
    QPushButton* m_zoomWideBtn;
    QPushButton* m_zoomTileBtn;

    // SDK相关
    int m_hUser;
    int m_playHandle;
    bool m_isRecording;
    bool m_isPreviewActive;
};
*/

class AddDeviceDialog : public QDialog
{
    Q_OBJECT

public:
    explicit AddDeviceDialog(QWidget *parent = nullptr);

    QString getDeviceName() const;
    QString getDeviceId() const;
    QString getUsername() const;
    QString getPassword() const;

private slots:
    void onOkClicked();

private:
    QLineEdit *deviceNameEdit;
    QLineEdit *deviceIdEdit;
    QLineEdit *usernameEdit;
    QLineEdit *passwordEdit;
};

class EditDeviceDialog : public QDialog
{
    Q_OBJECT

public:
    explicit EditDeviceDialog(const QMap<QString, QString>& deviceInfo, QWidget *parent = nullptr);

    QString getDeviceName() const;
    QString getDeviceId() const;
    QString getUsername() const;
    QString getPassword() const;

private slots:
    void onOkClicked();

private:
    QLineEdit *deviceNameEdit;
    QLineEdit *deviceIdEdit;
    QLineEdit *usernameEdit;
    QLineEdit *passwordEdit;
    QString originalDeviceId;  // 保存原始设备ID，用于更新时查找
};

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

    // 设备管理方法
    bool addDevice(const QString& deviceName, const QString& deviceId, const QString& username, const QString& password);
    bool removeDevice(const QString& deviceId);
    bool updateDevice(const QString& originalDeviceId, const QString& deviceName, const QString& deviceId, const QString& username, const QString& password);
    QMap<QString, QString> getDevice(const QString& deviceId);
    QList<QMap<QString, QString>> getAllDevices() const { return deviceList; }

    // 二维码检测器访问方法
    QRCodeDetector* getQRCodeDetector() const { return qrCodeDetector; }

    // 测试二维码检测器
    void testQRCodeDetector();

    // 设备登录和报警监听
    bool loginDevice(const QString& deviceId, const QString& username, const QString& password);
    void logoutDevice(const QString& deviceId);
    void startAlarmMonitoring();
    void stopAlarmMonitoring();
    bool startDeviceAlarmPush(const QString& deviceId);
    void stopDeviceAlarmPush(const QString& deviceId);

    // 并发和资源管理
    void initializeResourceManagement();
    void cleanupResourceManagement();
    void loginDevicesConcurrently();
    void checkDeviceStatus();
    bool isDeviceLoginInProgress(const QString& deviceId);
    void setDeviceLoginStatus(const QString& deviceId, bool status);
    bool getDeviceLoginStatus(const QString& deviceId);
    void addDeviceHandle(const QString& deviceId, int handle);
    void removeDeviceHandle(const QString& deviceId);
    int getDeviceHandle(const QString& deviceId);

    // 日志记录
    static void logSDKMessage(const QString& message);

public slots:
    // 报警抓图方法
    void performAlarmCapture(const QString& deviceId);

private:
    // 报警抓图辅助方法
    void performActualAlarmCapture(const QString& deviceId);
    void performDeviceAlarmCapture(const QString& deviceId);
    bool startDevicePreview(const QString& deviceId);

protected:
    // 重写关闭事件处理函数
    void closeEvent(QCloseEvent *event) override;

    // XCloudSDK 相关方法
    bool initializeSDK();
    void uninitializeSDK();
    bool connectToDevice(const QString& deviceId, const QString& username, const QString& password);
    bool startPreview(const QString& deviceId, QWidget* previewWidget);
    bool stopPreview(const QString& deviceId);
    bool captureImage(const QString& deviceId, const QString& filePath);
    bool getDeviceConfig(const QString& deviceId);  // 获取设备配置

    // SDK 回调处理
    static int CALLBACK onSDKMessage(XSDK_HANDLE hObject, int nMsgId, int nParam1, int nParam2, int nParam3, const char* szString, void* pObject, int64 lParam, int nSeq, void* pUserData, void* pMsg);

private slots:
    void showDeviceList();
    void showHomePage();
    void showAddDeviceDialog();
    void showEditDeviceDialog(const QString& deviceId);
    void onDeleteDevice(const QString& deviceId);
    void showPreviewWindow(const QString& deviceId);
    void onPreviewButtonClicked();
    void onEditButtonClicked();

private:
    void saveDeviceList();           // 保存设备列表到文件
    void loadDeviceList();           // 从文件加载设备列表
    void setupDeviceListPage();

    Ui::MainWindow *ui;
    QStackedWidget *stackedWidget;
    QWidget *homePage;
    QWidget *deviceListPage;
    
    // 设备列表存储
    QList<QMap<QString, QString>> deviceList;

    // XCloudSDK 相关成员变量
    bool sdkInitialized;
    int hUser;  // SDK用户句柄
    QMap<QString, int> devicePlayHandles;  // 设备ID -> 播放句柄的映射
    QMap<QString, QWidget*> previewWidgets; // 设备ID -> 预览窗口的映射

    // 设备登录和报警监听相关
    QMap<QString, int> deviceLoginHandles;  // 设备ID -> 登录句柄的映射
    QMap<QString, bool> deviceLoginStatus;  // 设备ID -> 登录状态的映射
    bool alarmMonitoringActive;             // 报警监听是否激活

    // 线程安全和并发控制
    mutable QMutex deviceMapMutex;          // 保护设备相关容器的互斥锁
    mutable QMutex loginMutex;              // 保护登录操作的互斥锁
    QThreadPool* deviceThreadPool;          // 设备操作线程池
    QTimer* deviceStatusTimer;              // 设备状态检查定时器

    // 资源管理
    static const int MAX_CONCURRENT_LOGINS = 5;  // 最大并发登录数
    static const int MAX_CONCURRENT_PREVIEWS = 10; // 最大并发预览数
    QSemaphore* loginSemaphore;             // 登录信号量
    QSemaphore* previewSemaphore;           // 预览信号量

    // 二维码检测器
    QRCodeDetector* qrCodeDetector;         // 二维码检测器实例
};
#endif // MAINWINDOW_H
