﻿#pragma once
#include "XBasic/XSDKPublic.h"
#include <string>
using namespace std;

#ifdef __cplusplus
extern "C"
{
#endif
    /**
     * @brief MD5加密
     * @param sInputPswBuf 密码输入
     * @param sOutputPswBuf 密码输出
     * @param nPswSize 密码长度
     * @return
     */
    XSDK_API int CALLBACK XCloudSDK_EncryptMD5(const char* sInputPswBuf, signed char* sOutputPswBuf, int nPswSize);

    /**
     * @brief 数据解密
     * @param sDevId 设备序列号
     * @param sEncData 加密数据
     * @param sOutDecData [输出]解密数据
     * @return
     */
    XSDK_API int CALLBACK XCloudSDK_DecDevRandomUserInfo(const char* sDevId, const char* sEncData, char* sOutDecData);

    /**
     * @brief 对数据进行AES加密---ECB
     * @param szSrcData 没加密的源数据
     * @param szKey 秘钥
     * @param szOutEncData [输出]加密后的数据---数据格式为HEX格式
     * @param nOutDataLen 缓冲区大小
     * @param bSecureRandomKey 是否随机秘钥
     * @return >0:成功;<=0:失败
     */
    XSDK_API int CALLBACK XCloudSDK_EncAesEcb128(const char* szSrcData,
                                                 int nDataLen,
                                                 const char* szKey,
                                                 char* szOutEncData,
                                                 int& nOutDataLen,
                                                 bool bSecureRandomKey);

    /**
     * @brief 对数据进行AES解密---ECB
     * @param szSrcData 加密的源数据---数据格式需要为HEX格式
     * @param szKey 秘钥
     * @param szOutDecData [输出]解密后的数据
     * @param nOutDataLen 缓冲区大小
     * @param bSecureRandomKey 是否随机秘钥
     * @return >0:成功;<=0:失败
     */
    XSDK_API int CALLBACK XCloudSDK_DecAesEcb128(const char* szSrcData,
                                                 int nDataLen,
                                                 const char* szKey,
                                                 char* szOutDecData,
                                                 int& nOutDataLen,
                                                 bool bSecureRandomKey);

    /**
     * @brief 对数据进行AES+BASE64加密---ECB
     * @param szSrcData 没加密的源数据
     * @param szKey 秘钥
     * @param szOutEncData [输出]加密后的数据
     * @param nOutDataLen 缓冲区大小
     * @return >0:成功;<=0:失败
     */
    XSDK_API int CALLBACK XCloudSDK_EncAesEcb128Base64(const char* szSrcData,
                                                       int nDataLen,
                                                       const char* szKey,
                                                       char* szOutEncData,
                                                       int& nOutDataLen);

    /**
     * @brief 对数据进行AES+BASE64解密---ECB
     * @param szSrcData 加密的源数据
     * @param szKey 秘钥
     * @param szOutDecData [输出]解密后的数据
     * @param nOutDataLen 缓冲区大小
     * @return >0:成功;<=0:失败
     */
    XSDK_API int CALLBACK XCloudSDK_DecAesEcb128Base64(const char* szSrcData,
                                                       int nDataLen,
                                                       const char* szKey,
                                                       char* szOutDecData,
                                                       int& nOutDataLen);

    /**
     * @brief 对数据进行AES加密---CBC
     * @param szSrcData 没加密的源数据
     * @param szKey 秘钥
     * @param szOutEncData [输出]加密后的数据---数据格式为HEX格式
     * @param nOutDataLen 缓冲区大小
     * @return >0:成功;<=0:失败
     */
    XSDK_API int CALLBACK XCloudSDK_EncAesCbc128(const char* szSrcData,
                                                 int nDataLen,
                                                 const char* szKey,
                                                 char* szOutEncData,
                                                 int& nOutDataLen);

    /**
     * @brief 对数据进行AES解密---CBC
     * @param szSrcData 加密的源数据---数据格式需要为HEX格式
     * @param szKey 秘钥
     * @param szOutDecData [输出]解密后的数据
     * @param nOutDataLen 缓冲区大小
     * @return >0:成功;<=0:失败
     */
    XSDK_API int CALLBACK XCloudSDK_DecAesCbc128(const char* szSrcData,
                                                 int nDataLen,
                                                 const char* szKey,
                                                 char* szOutDecData,
                                                 int& nOutDataLen);

    /**
     * @brief 对数据进行AES+BASE64加密---CBC
     * @param szSrcData 没加密的源数据
     * @param szKey 秘钥
     * @param szOutEncData [输出]加密后的数据
     * @param nOutDataLen 缓冲区大小
     * @return >0:成功;<=0:失败
     */
    XSDK_API int CALLBACK XCloudSDK_EncAesCbc128Base64(const char* szSrcData,
                                                       int nDataLen,
                                                       const char* szKey,
                                                       char* szOutEncData,
                                                       int& nOutDataLen);

    /**
     * @brief 对数据进行AES+BASE64解密---CBC
     * @param szSrcData 加密的源数据
     * @param szKey 秘钥
     * @param szOutDecData [输出]解密后的数据
     * @param nOutDataLen 缓冲区大小
     * @return >0:成功;<=0:失败
     */
    XSDK_API int CALLBACK XCloudSDK_DecAesCbc128Base64(const char* szSrcData,
                                                       int nDataLen,
                                                       const char* szKey,
                                                       char* szOutDecData,
                                                       int& nOutDataLen);

    /**
     * @brief 加密数据
     * @param szSrcData 输入数据
     * @param szOutEncData 加密得到的字符串
     * @param nOutDataLen 缓冲区大小
     * @return 加密得到的字符串长度
     */
    XSDK_API int CALLBACK XCloudSDK_EncGeneralDevInfo(const char* szSrcData, char* szOutEncData, int& nOutDataLen);

    /**
     * @brief 解密数据
     * @param szSrcData 输入数据
     * @param szOutEncData 解密得到的字符串
     * @param nOutDataLen 缓冲区大小
     * @return 解密得到的字符串长度
     */
    XSDK_API int CALLBACK XCloudSDK_DecGeneralDevInfo(const char* szSrcData, char* szOutEncData, int& nOutDataLen);

    /**
     * @brief 新的二维码解密（AES/CBC/PKCS5Padding解密）
     * @param szSrcData 输入数据
     * @param szOutData 解密得到的字符串
     * @return
     */
    XSDK_API int CALLBACK XCloudSDK_DecQRCodeDevInfo(const char* szSrcData, char* szOutData);

    /**
     * @brief 获取账户签名信息（包含Aes秘钥、时间戳和签名）
     * @param szOutTimeSignatureInfo 返回Aes秘钥、时间戳和签名信息（JSON格式）[out]
     * @param nOutTimeSignatureLen 地址长度[in][out]
     * @return >=0:成功;<0:失败
     * @remark JSON格式:
     * {
     *      "AesKey": "xxxxxxxxx",
     *      "Signature": "xxxxxxx",
     *      "TimeMillis": "00000011716448880938"
     * }
     */
    XSDK_API int CALLBACK XCloudSDK_GetAuthSignInfo(char* szOutTimeSignatureInfo, int& nOutTimeSignatureLen);

#ifdef __cplusplus
}
#endif