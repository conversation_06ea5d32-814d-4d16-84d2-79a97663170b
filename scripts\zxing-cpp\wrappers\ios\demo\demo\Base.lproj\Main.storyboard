<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="20037" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="yt5-A2-Fye">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="20020"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--Read-->
        <scene sceneID="tne-QT-ifu">
            <objects>
                <viewController id="BYZ-38-t0r" customClass="ViewController" customModule="demo" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="8bC-Xf-vdC">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <viewLayoutGuide key="safeArea" id="6Tk-OE-BBY"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    </view>
                    <navigationItem key="navigationItem" title="Read" id="FJL-kY-V3h">
                        <barButtonItem key="rightBarButtonItem" title="Write" id="cOY-Rz-7Sx">
                            <connections>
                                <segue destination="NS1-KJ-jgd" kind="show" id="kWB-XI-rqf"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="dkx-z0-nzr" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1047.8260869565217" y="137.94642857142856"/>
        </scene>
        <!--Write-->
        <scene sceneID="8Q2-Vq-Rob">
            <objects>
                <viewController id="NS1-KJ-jgd" customClass="WriteViewController" customModule="demo" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="II3-kA-EKz">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" borderStyle="roundedRect" placeholder="Type to generate code" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="AYU-Al-DLc">
                                <rect key="frame" x="20" y="108" width="374" height="34"/>
                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                <textInputTraits key="textInputTraits"/>
                                <connections>
                                    <action selector="textFieldChanged:" destination="NS1-KJ-jgd" eventType="editingChanged" id="06Y-mH-LeN"/>
                                </connections>
                            </textField>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="9ev-u1-fcI">
                                <rect key="frame" x="87" y="182" width="240" height="240"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="240" id="BXF-wO-RlT"/>
                                    <constraint firstAttribute="width" secondItem="9ev-u1-fcI" secondAttribute="height" multiplier="1:1" id="hse-Cr-0Zv"/>
                                </constraints>
                            </imageView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="esr-8S-vwU"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="9ev-u1-fcI" firstAttribute="top" secondItem="AYU-Al-DLc" secondAttribute="bottom" constant="40" id="GYN-Ow-aWc"/>
                            <constraint firstItem="9ev-u1-fcI" firstAttribute="centerX" secondItem="esr-8S-vwU" secondAttribute="centerX" id="dtl-Nu-gKp"/>
                            <constraint firstItem="AYU-Al-DLc" firstAttribute="top" secondItem="esr-8S-vwU" secondAttribute="top" constant="20" id="fKy-vm-mKO"/>
                            <constraint firstItem="esr-8S-vwU" firstAttribute="trailing" secondItem="AYU-Al-DLc" secondAttribute="trailing" constant="20" id="hk7-7R-Sd5"/>
                            <constraint firstItem="AYU-Al-DLc" firstAttribute="leading" secondItem="esr-8S-vwU" secondAttribute="leading" constant="20" id="mvP-qH-VeG"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" title="Write" id="zdR-J6-ZO8"/>
                    <connections>
                        <outlet property="imageView" destination="9ev-u1-fcI" id="5NL-Mg-JRd"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="n6S-jS-clP" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1839.1304347826087" y="137.94642857142856"/>
        </scene>
        <!--Navigation Controller-->
        <scene sceneID="8xG-zr-hYL">
            <objects>
                <navigationController automaticallyAdjustsScrollViewInsets="NO" id="yt5-A2-Fye" sceneMemberID="viewController">
                    <toolbarItems/>
                    <navigationBar key="navigationBar" contentMode="scaleToFill" id="LMB-hf-Xg8">
                        <rect key="frame" x="0.0" y="44" width="414" height="44"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </navigationBar>
                    <nil name="viewControllers"/>
                    <connections>
                        <segue destination="BYZ-38-t0r" kind="relationship" relationship="rootViewController" id="8AK-79-EBZ"/>
                    </connections>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="zqG-ca-UiG" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="137.68115942028987" y="137.94642857142856"/>
        </scene>
    </scenes>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
