﻿#pragma once
#include "XNetSDK/Json_Header/JObject_NetSDK.h"
NS_NETSDK_CFG_BEGIN

#define JK_NetWork_NetDHCP "NetWork.NetDHCP"
class NetWork_NetDHCP: public JObject
{
public:
    JBoolObj Enable;   ///< 是否开启
    JStrObj Interface; ///< 网卡名称

public:
    NetWork_NetDHCP(JObject* pParent = NULL, const char* szName = JK_NetWork_NetDHCP)
        : JObject(pParent, szName)
        , Enable(this, "Enable")
        , Interface(this, "Interface"){};

    ~NetWork_NetDHCP(void){};
};

NS_NETSDK_CFG_END
