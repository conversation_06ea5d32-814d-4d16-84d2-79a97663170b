#!/usr/bin/env python3
"""
ZXing-cpp 下载和构建脚本
用于自动下载、编译和安装ZXing-cpp库
"""

import os
import sys
import subprocess
import shutil
import urllib.request
import zipfile
from pathlib import Path

def find_cmake():
    """查找CMake"""
    # 常见的CMake路径
    cmake_paths = [
        Path("D:/Qt/Tools/CMake_64/bin/cmake.exe"),
        Path("C:/Qt/Tools/CMake_64/bin/cmake.exe"),
        Path("C:/Program Files/CMake/bin/cmake.exe"),
        Path("C:/Program Files (x86)/CMake/bin/cmake.exe"),
        Path("C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/cmake.exe"),
    ]

    for path in cmake_paths:
        if path.exists():
            print(f"找到CMake: {path}")
            return str(path)

    # 尝试在PATH中查找
    try:
        result = subprocess.run("where cmake", shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            cmake_path = result.stdout.strip().split('\n')[0]
            print(f"在PATH中找到CMake: {cmake_path}")
            return cmake_path
    except:
        pass

    return None

def run_command(cmd, cwd=None):
    """运行命令并检查结果"""
    print(f"Running: {cmd}")
    result = subprocess.run(cmd, shell=True, cwd=cwd, capture_output=True, text=True)
    if result.returncode != 0:
        print(f"Error running command: {cmd}")
        print(f"stdout: {result.stdout}")
        print(f"stderr: {result.stderr}")
        return False
    print(f"Success: {result.stdout}")
    return True

def download_file(url, filename, chunk_size=8192):
    """下载文件并显示进度"""
    print(f"正在下载: {url}")

    try:
        with urllib.request.urlopen(url) as response:
            total_size = int(response.headers.get('Content-Length', 0))
            downloaded = 0

            with open(filename, 'wb') as f:
                while True:
                    chunk = response.read(chunk_size)
                    if not chunk:
                        break
                    f.write(chunk)
                    downloaded += len(chunk)

                    if total_size > 0:
                        progress = (downloaded / total_size) * 100
                        print(f"\r下载进度: {progress:.1f}%", end="", flush=True)

            print(f"\n下载完成: {filename}")
            return True

    except Exception as e:
        print(f"\n下载失败: {e}")
        return False

def extract_zip(zip_path, extract_to):
    """解压ZIP文件"""
    print(f"正在解压: {zip_path}")
    try:
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(extract_to)
        print("解压完成")
        return True
    except Exception as e:
        print(f"解压失败: {e}")
        return False

def download_zxing(work_dir):
    """下载ZXing-cpp源码"""
    zxing_url = "https://github.com/zxing-cpp/zxing-cpp/archive/refs/tags/v2.2.1.zip"
    zip_file = os.path.join(work_dir, "zxing-cpp-2.2.1.zip")
    extract_dir = os.path.join(work_dir, "zxing-cpp-2.2.1")
    final_dir = os.path.join(work_dir, "zxing-cpp")

    print("开始下载ZXing-cpp...")

    if not download_file(zxing_url, zip_file):
        return False

    if not extract_zip(zip_file, work_dir):
        return False

    # 重命名目录
    if os.path.exists(extract_dir):
        if os.path.exists(final_dir):
            shutil.rmtree(final_dir)
        os.rename(extract_dir, final_dir)

    # 清理zip文件
    os.remove(zip_file)
    print("ZXing-cpp源码下载完成")
    return True

def build_zxing(work_dir):
    """编译ZXing-cpp"""
    # 查找CMake
    cmake_path = find_cmake()
    if not cmake_path:
        print("错误: 未找到CMake!")
        print("请安装CMake或确保CMake在PATH中")
        print("可以从以下位置下载CMake:")
        print("- https://cmake.org/download/")
        print("- 或者安装Qt Creator，它包含CMake")
        return False

    zxing_dir = os.path.join(work_dir, "zxing-cpp")
    build_dir = os.path.join(zxing_dir, "build")

    if not os.path.exists(zxing_dir):
        print(f"ZXing source directory not found: {zxing_dir}")
        return False

    # 创建构建目录
    os.makedirs(build_dir, exist_ok=True)

    # 配置CMake
    cmake_cmd = [
        f'"{cmake_path}"', "..",
        "-DCMAKE_BUILD_TYPE=Release",
        "-DBUILD_SHARED_LIBS=ON",
        "-DBUILD_EXAMPLES=OFF",
        "-DBUILD_BLACKBOX_TESTS=OFF",
        "-DBUILD_UNIT_TESTS=OFF"
    ]

    print("正在配置CMake...")
    if not run_command(" ".join(cmake_cmd), cwd=build_dir):
        return False

    # 编译
    print("正在编译ZXing-cpp...")
    if not run_command(f'"{cmake_path}" --build . --config Release', cwd=build_dir):
        return False

    print("ZXing-cpp编译成功!")
    return True

def install_zxing(third_party_dir):
    """重新组织ZXing-cpp文件结构"""
    zxing_source_dir = os.path.join(third_party_dir, "zxing-cpp")
    build_dir = os.path.join(zxing_source_dir, "build")
    zxing_final_dir = os.path.join(third_party_dir, "zxing")

    # 创建最终的zxing目录结构
    os.makedirs(zxing_final_dir, exist_ok=True)
    os.makedirs(os.path.join(zxing_final_dir, "include"), exist_ok=True)
    os.makedirs(os.path.join(zxing_final_dir, "lib"), exist_ok=True)
    os.makedirs(os.path.join(zxing_final_dir, "bin"), exist_ok=True)

    # 复制头文件
    src_include = os.path.join(zxing_source_dir, "core", "src")
    dst_include = os.path.join(zxing_final_dir, "include")

    if os.path.exists(src_include):
        shutil.copytree(src_include, os.path.join(dst_include, "ZXing"), dirs_exist_ok=True)
        print(f"复制头文件: {src_include} -> {dst_include}")

    # 复制库文件
    lib_patterns = ["*.dll", "*.lib", "*.a", "*.so"]
    for pattern in lib_patterns:
        for lib_file in Path(build_dir).rglob(pattern):
            if "ZXing" in str(lib_file):
                shutil.copy2(lib_file, os.path.join(zxing_final_dir, "lib"))
                print(f"复制库文件: {lib_file}")

    # 复制DLL文件到bin目录
    for dll_file in Path(build_dir).rglob("*.dll"):
        if "ZXing" in str(dll_file):
            shutil.copy2(dll_file, os.path.join(zxing_final_dir, "bin"))
            print(f"复制DLL文件: {dll_file}")

    # 清理源码目录（保留最终的zxing目录）
    if os.path.exists(zxing_source_dir):
        print(f"清理源码目录: {zxing_source_dir}")
        shutil.rmtree(zxing_source_dir)

    print(f"ZXing-cpp最终安装到: {zxing_final_dir}")
    return True

def main():
    """主函数"""
    # 获取项目根目录（scripts的上级目录）
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(script_dir)

    # 直接在third_party目录下工作
    third_party_dir = os.path.join(project_root, "third_party")
    os.makedirs(third_party_dir, exist_ok=True)

    print("=== ZXing-cpp 安装程序 ===")
    print(f"项目根目录: {project_root}")
    print(f"third_party目录: {third_party_dir}")

    # 检查是否已经存在
    zxing_install_dir = os.path.join(third_party_dir, "zxing")
    if os.path.exists(zxing_install_dir):
        print("ZXing已存在于third_party目录中")
        response = input("是否重新安装? (y/N): ")
        if response.lower() != 'y':
            print("安装已取消")
            return
        else:
            shutil.rmtree(zxing_install_dir)

    # 下载
    print("\n步骤 1/3: 下载ZXing-cpp源码到third_party目录")
    if not download_zxing(third_party_dir):
        print("❌ 下载ZXing-cpp失败")
        sys.exit(1)
    print("✅ 下载完成")

    # 编译
    print("\n步骤 2/3: 编译ZXing-cpp")
    if not build_zxing(third_party_dir):
        print("❌ 编译ZXing-cpp失败")
        print("\n可能的解决方案:")
        print("1. 安装CMake: https://cmake.org/download/")
        print("2. 安装Qt Creator (包含CMake)")
        print("3. 使用vcpkg安装预编译版本: vcpkg install zxing-cpp")
        sys.exit(1)
    print("✅ 编译完成")

    # 安装（重新组织文件结构）
    print("\n步骤 3/3: 整理ZXing-cpp文件结构")
    if not install_zxing(third_party_dir):
        print("❌ 整理ZXing-cpp失败")
        sys.exit(1)
    print("✅ 整理完成")

    print("\n🎉 ZXing-cpp安装成功!")
    print(f"安装位置: {zxing_install_dir}")
    print("现在可以编译项目了")

if __name__ == "__main__":
    main()
