#!/usr/bin/env python3
"""
ZXing-cpp 下载和构建脚本
用于自动下载、编译和安装ZXing-cpp库
"""

import os
import sys
import subprocess
import shutil
import urllib.request
import zipfile
from pathlib import Path

def run_command(cmd, cwd=None):
    """运行命令并检查结果"""
    print(f"Running: {cmd}")
    result = subprocess.run(cmd, shell=True, cwd=cwd, capture_output=True, text=True)
    if result.returncode != 0:
        print(f"Error running command: {cmd}")
        print(f"stdout: {result.stdout}")
        print(f"stderr: {result.stderr}")
        return False
    return True

def download_zxing():
    """下载ZXing-cpp源码"""
    zxing_url = "https://github.com/zxing-cpp/zxing-cpp/archive/refs/tags/v2.2.1.zip"
    zip_file = "zxing-cpp-2.2.1.zip"
    
    print("Downloading ZXing-cpp...")
    try:
        urllib.request.urlretrieve(zxing_url, zip_file)
        print(f"Downloaded {zip_file}")
        
        # 解压
        with zipfile.ZipFile(zip_file, 'r') as zip_ref:
            zip_ref.extractall(".")
        
        # 重命名目录
        if os.path.exists("zxing-cpp-2.2.1"):
            if os.path.exists("zxing-cpp"):
                shutil.rmtree("zxing-cpp")
            os.rename("zxing-cpp-2.2.1", "zxing-cpp")
        
        # 清理zip文件
        os.remove(zip_file)
        return True
    except Exception as e:
        print(f"Error downloading ZXing: {e}")
        return False

def build_zxing():
    """编译ZXing-cpp"""
    zxing_dir = "zxing-cpp"
    build_dir = os.path.join(zxing_dir, "build")
    
    if not os.path.exists(zxing_dir):
        print("ZXing source directory not found!")
        return False
    
    # 创建构建目录
    os.makedirs(build_dir, exist_ok=True)
    
    # 配置CMake
    cmake_cmd = [
        "cmake", "..",
        "-DCMAKE_BUILD_TYPE=Release",
        "-DBUILD_SHARED_LIBS=ON",
        "-DBUILD_EXAMPLES=OFF",
        "-DBUILD_BLACKBOX_TESTS=OFF",
        "-DBUILD_UNIT_TESTS=OFF"
    ]
    
    if not run_command(" ".join(cmake_cmd), cwd=build_dir):
        return False
    
    # 编译
    if not run_command("cmake --build . --config Release", cwd=build_dir):
        return False
    
    print("ZXing-cpp built successfully!")
    return True

def install_zxing():
    """安装ZXing-cpp到third_party目录"""
    zxing_dir = "zxing-cpp"
    build_dir = os.path.join(zxing_dir, "build")
    install_dir = os.path.join("..", "third_party", "zxing")
    
    # 创建安装目录
    os.makedirs(install_dir, exist_ok=True)
    os.makedirs(os.path.join(install_dir, "include"), exist_ok=True)
    os.makedirs(os.path.join(install_dir, "lib"), exist_ok=True)
    os.makedirs(os.path.join(install_dir, "bin"), exist_ok=True)
    
    # 复制头文件
    src_include = os.path.join(zxing_dir, "core", "src")
    dst_include = os.path.join(install_dir, "include")
    
    if os.path.exists(src_include):
        shutil.copytree(src_include, os.path.join(dst_include, "ZXing"), dirs_exist_ok=True)
    
    # 复制库文件
    lib_patterns = ["*.dll", "*.lib", "*.a", "*.so"]
    for pattern in lib_patterns:
        for lib_file in Path(build_dir).rglob(pattern):
            if "ZXing" in str(lib_file):
                shutil.copy2(lib_file, os.path.join(install_dir, "lib"))
                print(f"Copied {lib_file}")
    
    # 复制DLL文件到bin目录
    for dll_file in Path(build_dir).rglob("*.dll"):
        if "ZXing" in str(dll_file):
            shutil.copy2(dll_file, os.path.join(install_dir, "bin"))
            print(f"Copied {dll_file}")
    
    print(f"ZXing-cpp installed to {install_dir}")
    return True

def main():
    """主函数"""
    # 切换到scripts目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    print("Setting up ZXing-cpp...")
    
    # 检查是否已经存在
    if os.path.exists("../third_party/zxing"):
        print("ZXing already exists in third_party directory")
        return
    
    # 下载
    if not download_zxing():
        print("Failed to download ZXing-cpp")
        sys.exit(1)
    
    # 编译
    if not build_zxing():
        print("Failed to build ZXing-cpp")
        sys.exit(1)
    
    # 安装
    if not install_zxing():
        print("Failed to install ZXing-cpp")
        sys.exit(1)
    
    print("ZXing-cpp setup completed successfully!")

if __name__ == "__main__":
    main()
