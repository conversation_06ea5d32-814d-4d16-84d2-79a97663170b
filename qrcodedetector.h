#ifndef QRCODEDETECTOR_H
#define QRCODEDETECTOR_H

#include <QString>
#include <QStringList>
#include <QDebug>
#include <opencv2/opencv.hpp>
// #include <opencv2/wechat_qrcode.hpp>  // 注释掉微信二维码模块

/**
 * @brief 二维码检测器类
 * 使用OpenCV的标准二维码检测器进行二维码识别
 */
class QRCodeDetector
{
public:
    /**
     * @brief 构造函数
     */
    QRCodeDetector();
    
    /**
     * @brief 析构函数
     */
    ~QRCodeDetector();
    
    /**
     * @brief 初始化检测器
     * @param modelPath 模型文件路径（标准QRCodeDetector不需要模型文件，此参数保留兼容性）
     * @return 初始化是否成功
     */
    bool initialize(const QString& modelPath = "");
    
    /**
     * @brief 检测图片中的二维码
     * @param imagePath 图片文件路径
     * @return 检测到的二维码内容列表
     */
    QStringList detectQRCodes(const QString& imagePath);
    
    /**
     * @brief 检测cv::Mat图像中的二维码
     * @param image OpenCV图像
     * @return 检测到的二维码内容列表
     */
    QStringList detectQRCodes(const cv::Mat& image);
    
    /**
     * @brief 检查检测器是否已初始化
     * @return 是否已初始化
     */
    bool isInitialized() const { return m_initialized; }
    
    /**
     * @brief 获取最后的错误信息
     * @return 错误信息
     */
    QString getLastError() const { return m_lastError; }

private:
    // cv::wechat_qrcode::WeChatQRCode* m_detector;  ///< 微信二维码检测器（已注释）
    cv::QRCodeDetector m_detector;                ///< 标准OpenCV二维码检测器
    bool m_initialized;                           ///< 是否已初始化
    QString m_lastError;                          ///< 最后的错误信息
    
    /**
     * @brief 设置错误信息
     * @param error 错误信息
     */
    void setError(const QString& error);

    /**
     * @brief 安全读取图片文件（支持中文路径）
     * @param imagePath 图片文件路径
     * @return OpenCV图像，如果读取失败则返回空图像
     */
    cv::Mat readImageSafely(const QString& imagePath);
};

#endif // QRCODEDETECTOR_H
