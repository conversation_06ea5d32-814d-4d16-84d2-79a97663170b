﻿#pragma once
#include "XNetSDK/Json_Header/JObject_NetSDK.h"
NS_NETSDK_CFG_BEGIN

#define JK_General_General "General.General"
class General_General: public JObject
{
public:
    JIntObj AutoLogout;         // 本地菜单自动注销(分钟)    [0, 120]
    JIntObj FontSize;           // 矢量字体大小
    JIntObj IranCalendarEnable; // 否启用伊朗日历，1表示启用，0表示不启用
    JIntObj LocalNo;            // 本机编号:[0, 998]
    JStrObj MachineName;        // 机器名
    JStrObj OverWrite;          // 硬盘满时处理 "OverWrite（iOverWrite=1）", "StopRecord（iOverWrite=0）"
    JIntObj ScreenAutoShutdown;
    JIntObj ScreenSaveTime;
    JIntObj SnapInterval;
    JStrObj VideoOutPut; // 输出模式

public:
    General_General(JObject* pParent = NULL, const char* szName = JK_General_General)
        : JObject(pParent, szName)
        , AutoLogout(this, "AutoLogout")
        , FontSize(this, "FontSize")
        , IranCalendarEnable(this, "IranCalendarEnable")
        , LocalNo(this, "LocalNo")
        , MachineName(this, "MachineName")
        , OverWrite(this, "OverWrite")
        , ScreenAutoShutdown(this, "ScreenAutoShutdown")
        , ScreenSaveTime(this, "ScreenSaveTime")
        , SnapInterval(this, "SnapInterval")
        , VideoOutPut(this, "VideoOutPut"){};

    ~General_General(void){};
};

NS_NETSDK_CFG_END