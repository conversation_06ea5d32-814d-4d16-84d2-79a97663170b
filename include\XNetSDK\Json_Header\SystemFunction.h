﻿#pragma once
#include "XNetSDK/Json_Header/JObject_NetSDK.h"
#include "XNetSDK/Json_Header/AlarmFunction.h"
#include "XNetSDK/Json_Header/CommFunction.h"
#include "XNetSDK/Json_Header/EncodeFunction.h"
#include "XNetSDK/Json_Header/InputMethod.h"
#include "XNetSDK/Json_Header/MobileDVR.h"
#include "XNetSDK/Json_Header/NetServerFunction.h"
#include "XNetSDK/Json_Header/OtherFunction.h"
#include "XNetSDK/Json_Header/PreviewFunction.h"
#include "XNetSDK/Json_Header/TipShow.h"
NS_NETSDK_CFG_BEGIN

#define JK_SystemFunction "SystemFunction"
#define JK_SystemFunction_MsgId 1360
class SystemFunction: public JObject
{
public:
    AlarmFunction mAlarmFunction;
    CommFunction mCommFunction;
    EncodeFunction mEncodeFunction;
    InputMethod mInputMethod;
    MobileDVR mMobileDVR;
    NetServerFunction mNetServerFunction;
    OtherFunction mOtherFunction;
    PreviewFunction mPreviewFunction;
    TipShow mTipShow;

public:
    SystemFunction(JObject* pParent = NULL, const char* szName = JK_SystemFunction)
        : JObject(pParent, szName)
        , mAlarmFunction(this, "AlarmFunction")
        , mCommFunction(this, "CommFunction")
        , mEncodeFunction(this, "EncodeFunction")
        , mInputMethod(this, "InputMethod")
        , mMobileDVR(this, "MobileDVR")
        , mNetServerFunction(this, "NetServerFunction")
        , mOtherFunction(this, "OtherFunction")
        , mPreviewFunction(this, "PreviewFunction")
        , mTipShow(this, "TipShow"){};

    ~SystemFunction(void){};
};

NS_NETSDK_CFG_END